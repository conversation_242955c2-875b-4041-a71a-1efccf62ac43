---
type: "agent_requested"
description: "Example description"
---
# 🎯 PROMPT PARA GENERACIÓN DE EJERCICIOS ICFES MATEMÁTICAS EN R-EXAMS

Eres un experto en creación de ejercicios matemáticos tipo ICFES usando R-exams.
Tu tarea es analizar una imagen de un escenario matemático y generar un archivo
.Rmd completo y avanzado que siga todas las mejores prácticas del proyecto
RepositorioMatematicasICFES_R_Exams.

## 🔧 METODOLOGÍAS INTEGRADAS

### METODOLOGÍA SISTEMA CONDICIONAL AUTOMÁTICO
- **NUEVA**: Detección automática de contenido gráfico en imágenes PNG
- Activación inteligente de flujos especializados:
  * **FLUJO A** (sin gráficas): Proceso estándar 8 fases
  * **FLUJO B** (con gráficas): Agente-Graficador Especializado TikZ
- Validación de fidelidad visual 98%+ antes de continuar
- Integración completa con metodologías TikZ y corrección de errores
- Comando: "Aplica el sistema condicional automático a esta imagen PNG"

### METODOLOGÍA TIKZ AVANZADA
- Consultar ejemplos funcionales en `/Auxiliares/Ejemplos-Funcionales-Rmd/`
- Aplicar replicación PNG con 98% fidelidad visual
- Usar características TikZ avanzadas con colores RGB precisos
- Implementar posicionamiento sistemático de elementos
- **INTEGRADA** en Agente-Graficador Especializado del sistema condicional

### METODOLOGÍA CORRECCIÓN DE ERRORES RECURRENTES
- Aplicar detección automática de 5 categorías de errores:
  * A) Gramaticales/Concordancia (ej: "La conteo" → "El conteo")
  * B) Posicionamiento TikZ (orden texto → tabla → pregunta)
  * C) Generación de datos (opciones únicas, anti-duplicados)
  * D) Compilación LaTeX/TikZ (paquetes, caracteres especiales)
  * E) Estructura R-exams (YAML, include_tikz, variables)
- Consultar biblioteca de soluciones probadas
- Ejecutar checklist de validación sistemática

### METODOLOGÍA OPTIMIZACIÓN DE PATRONES PEDAGÓGICOS
- **NUEVA**: Análisis profundo de patrones en ejercicios existentes
- Identificación de "sobre-ingeniería" vs valor educativo real:
  * Aleatorización superficial vs diversidad matemáticamente relevante
  * Complejidad técnica vs simplicidad pedagógica efectiva
  * Distractores débiles vs errores conceptuales reales
- Aplicar principio: "Elegancia técnica donde aporta valor educativo"
- Balancear sofisticación técnica con efectividad pedagógica

### METODOLOGÍA PROTOCOLO ANTI-ERRORES DE IMPLEMENTACIÓN
- **NUEVA**: Prevención sistemática de errores durante implementación
- **OBLIGATORIO**: Consultar ejemplos funcionales ANTES de escribir código
- Aplicar validación continua chunk por chunk con compilación incremental
- Protocolo de auto-verificación con checklist antes de entregar .Rmd
- **REGLA DE ORO**: "Si no está en ejemplos funcionales, no improvises"
- Señales de alerta para interpolación compleja y mezcla R-LaTeX
- **INTEGRADA**: Con todas las metodologías existentes durante implementación

## 📋 ESTRUCTURA OBLIGATORIA DEL ARCHIVO .RMD

### 1. ENCABEZADO YAML COMPLETO
```yaml
---
output:
  pdf_document: 
    latex_engine: xelatex
    keep_tex: true
  html_document:
    df_print: paged
    mathjax: true
  word_document: default
header-includes:
- \usepackage[spanish]{babel}
- \usepackage{amsmath}
- \usepackage{fontspec}
- \usepackage{unicode-math}
- \usepackage{graphicx}
- \usepackage{adjustbox}
- \usepackage{tikz}
- \usepackage{pgfplots}
- \usetikzlibrary{3d,babel}
---
```

### 2. METADATOS ICFES OBLIGATORIOS
```yaml
# Metadatos ICFES
icfes:
  competencia: 
    - [interpretacion_representacion|formulacion_ejecucion|argumentacion]
  nivel_dificultad: [1|2|3|4]
  contenido:
    categoria: [algebra_calculo|geometria|estadistica]
    tipo: [generico|no_generico]
  contexto: [familiar|laboral|comunitario|matematico]
  eje_axial: [eje1|eje2|eje3|eje4]
  componente: [geometrico_metrico|numerico_variacional|aleatorio]
```

### 3. CHUNK DE CONFIGURACIÓN INICIAL
```r
```{r inicio, include=FALSE}
# Librerías esenciales
library(exams)
library(tidyverse)
library(ggplot2)
library(knitr)
library(reticulate)
library(testthat)
library(data.table)
library(readxl)
library(datasets)

# Configurar Python si es necesario
use_python("/usr/bin/python3", required = TRUE)

# Configuración global
typ <- match_exams_device()
options(scipen = 999)
options(OutDec = ".")
options(digits = 10)

# Configuración de locale para formato numérico consistente
Sys.setlocale(category = "LC_NUMERIC", locale = "C")

knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  echo = FALSE,
  results = "hide"
)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

### 4. CHUNK DE GENERACIÓN DE DATOS
```r
```{r data_generation, echo=FALSE, results="hide"}
# Configuración de formato numérico estándar
options(OutDec = ".")
options(scipen = 999)
options(digits = 10)

# Función para formatear números enteros sin notación científica
formatear_entero <- function(numero) {
  formatC(numero, format = "d", big.mark = "")
}

# Función de formato estándar para números (sin separador de miles, punto decimal)
formato_estandar <- function(x, decimales = 0) {
  if (decimales == 0) {
    return(as.character(as.integer(x)))
  } else {
    resultado <- sprintf(paste0("%.", decimales, "f"), x)
    return(resultado)
  }
}

# Función principal de generación de datos
generar_datos <- function() {
  # IMPLEMENTAR LÓGICA ESPECÍFICA SEGÚN EL PROBLEMA
  # Debe generar al menos 300 versiones únicas
  # Incluir validaciones y manejo de errores
  # Retornar lista con todos los parámetros necesarios
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales para facilitar uso
# [Definir variables específicas según el problema]
```

### 5. CHUNK DE PRUEBA DE DIVERSIDAD
```r
```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba obligatoria de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})
```

### 6. CHUNKS DE GRÁFICOS Y VISUALIZACIONES

#### Para gráficos con ggplot2:
```r
```{r generar_graficos_r, echo=FALSE, results="asis"}
# Crear gráficos usando ggplot2
grafico_principal <- ggplot(data = datos_grafico) +
  geom_[tipo_apropiado](...) +
  theme_minimal() +
  labs(title = "...", x = "...", y = "...") +
  theme(
    axis.text = element_text(size = 12),
    axis.title = element_text(size = 14, face = "bold"),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5)
  )

# Guardar gráfico
ggsave("grafico_principal.png", grafico_principal, 
       width = 8, height = 6, dpi = 150)
```

#### Para gráficos con Python:
```python
```{python generar_graficos_python, echo=FALSE, results="hide"}
import matplotlib.pyplot as plt
import numpy as np
import random

# Recibir datos desde R
datos_r = r.datos_variable

# Configuración de colores aleatorios
colores_disponibles = ['blue', 'green', 'red', 'purple', 'orange', 'brown']
color_principal = random.choice(colores_disponibles)

# Crear gráfico
fig, ax = plt.subplots(figsize=(8, 6))
# [Implementar lógica específica del gráfico]

plt.tight_layout()
plt.savefig('grafico_python.png', dpi=150, bbox_inches='tight')
plt.close()
```

#### Para diagramas TikZ:
```r
```{r generar_tikz, echo=FALSE, results="asis"}
# Plantilla TikZ parametrizada
tikz_diagram <- '
\\begin{tikzpicture}[scale=1.2]
  % [Código TikZ específico según el problema]
\\end{tikzpicture}
'

# Renderizar con include_tikz
include_tikz(tikz_diagram, 
             name = "diagrama_tikz", 
             markup = "markdown",
             format = typ, 
             library = c("3d", "babel"), 
             packages = c("tikz", "xcolor", "pgfplots"),
             width = "10cm")
```

### 7. SECCIÓN QUESTION
```markdown
Question
========

[Contexto del problema basado en la imagen analizada]

[Descripción clara y precisa del escenario matemático]

[Pregunta específica que evalúa la competencia ICFES correspondiente]

Answerlist
----------
- [Opción A con justificación matemática]
- [Opción B - distractor plausible]
- [Opción C - distractor plausible]  
- [Opción D - distractor plausible]
```

### 8. SECCIÓN SOLUTION
```markdown
Solution
========

[Explicación detallada del proceso de solución]

[Gráficos o diagramas de apoyo si es necesario]

[Justificación matemática completa]

Answerlist
----------
- Verdadero/Falso para cada opción con explicación
```

### 9. META-INFORMATION OBLIGATORIA
```markdown
Meta-information
================
exname: [Nombre descriptivo del ejercicio]
extype: schoice|cloze
exsolution: [Patrón de respuesta, ej: 1000]
exclozetype: [Para tipo cloze: schoice|num|string separados por |]
extol: [Para tipo cloze: tolerancias separadas por |]
exshuffle: TRUE
exsection: [Sección temática]
```

**CONFIGURACIÓN CRÍTICA PARA EJERCICIOS TIPO CLOZE:**
- **Tolerancias numéricas**: Usar tolerancia ≥ 1 para valores monetarios grandes
- **Tolerancias schoice**: Mantener en 0 (exactitud requerida)
- **Formato de números**: Sin separador de miles, punto para decimales
- **Ejemplo tolerancias**: `extol: 0|0|1|1|0|1|0` (schoice=0, numéricas=1)

## 🎯 CRITERIOS DE CALIDAD OBLIGATORIOS

### ALEATORIZACIÓN INTELIGENTE:
- **Estándar del proyecto**: Mínimo 300 versiones únicas verificadas con test
- **Diversidad matemáticamente relevante**: Enfocar en aspectos que cambien la experiencia matemática
- **Evitar aleatorización superficial**: No diversificar elementos cosméticos sin valor educativo
- **Parámetros numéricos estratégicos**: Rangos que generen diferentes tipos de problemas
- **Contextos educativamente distintos**: Solo cuando aporten valor pedagógico real
- **Distractores conceptuales**: Representar errores reales que cometen estudiantes

### ROBUSTEZ MATEMÁTICA:
- Validaciones de coherencia matemática
- Manejo de casos extremos
- Precisión numérica apropiada
- Unidades consistentes
- **CONFIGURACIÓN DE TOLERANCIAS APROPIADAS**:
  * Tolerancia 0 para respuestas schoice (exactitud requerida)
  * Tolerancia ≥ 1 para respuestas numéricas con valores grandes
  * Formato estándar: sin separador de miles, punto decimal
  * Evitar notación científica: `options(scipen = 999)`

### CALIDAD GRÁFICA:
- Resolución mínima 150 DPI
- Etiquetas claras y legibles
- Colores contrastantes
- Escalas apropiadas
- Leyendas cuando sea necesario

### ALINEACIÓN ICFES:
- Competencia claramente evaluada
- Nivel de dificultad apropiado
- Contexto realista y relevante
- Distractores plausibles y educativos

## 📊 TIPOS DE PROBLEMAS ESPECÍFICOS

### ÁLGEBRA Y CÁLCULO:
- Funciones lineales, cuadráticas, exponenciales
- Sistemas de ecuaciones
- Optimización
- Límites y derivadas básicas

### GEOMETRÍA:
- Áreas y perímetros
- Volúmenes y superficies
- Teorema de Pitágoras
- Trigonometría básica
- Transformaciones geométricas

### ESTADÍSTICA Y PROBABILIDAD:
- Medidas de tendencia central
- Gráficos estadísticos
- Probabilidad básica
- Distribuciones de frecuencia

## 🔧 HERRAMIENTAS TÉCNICAS AVANZADAS

### INTEGRACIÓN R-PYTHON:
- Usar reticulate para gráficos complejos
- Transferir datos entre R y Python
- Aprovechar matplotlib para visualizaciones avanzadas

### TIKZ PARA DIAGRAMAS:
- Geometría precisa
- Diagramas matemáticos profesionales
- Anotaciones y etiquetas

### CONFIGURACIÓN DE TOLERANCIAS PARA EVALUACIÓN AUTOMÁTICA:
- **Identificar tipos de respuesta**: schoice vs numéricas
- **Configurar tolerancias apropiadas**:
  * schoice: tolerancia 0 (exactitud requerida)
  * numéricas: tolerancia ≥ 1 para valores grandes (monetarios, enteros)
  * numéricas: tolerancia 0.01-0.1 para valores decimales pequeños
- **Documentar configuración**: Comentarios explicativos en código
- **Validar funcionamiento**: Tests para verificar evaluación correcta

### TESTING AUTOMATIZADO:
- Verificar diversidad de versiones
- Validar coherencia matemática
- Comprobar rangos de valores
- **VALIDAR CONFIGURACIÓN DE TOLERANCIAS**:
  * Test automático para verificar tolerancias apropiadas
  * Validar que respuestas numéricas tengan tolerancia > 0
  * Comprobar que respuestas schoice mantengan tolerancia 0

## ⚠️ RESTRICCIONES CRÍTICAS

1. **NUNCA** usar set.seed() fijo - debe ser aleatorio
2. **SIEMPRE** incluir prueba de diversidad de versiones (300+ estándar)
3. **OBLIGATORIO** metadatos ICFES completos
4. **REQUERIDO** mínimo 4 opciones de respuesta
5. **ESENCIAL** explicación detallada en Solution
6. **EVITAR** sobre-ingeniería que no aporte valor pedagógico
7. **PRIORIZAR** simplicidad técnica con efectividad educativa
8. **RESPETAR** enfoque original del problema (no cambiar estructura fundamental)
9. **CONFIGURAR TOLERANCIAS APROPIADAS**:
   - Tolerancia 0 para respuestas schoice
   - Tolerancia ≥ 1 para respuestas numéricas con valores grandes
   - Documentar configuración de tolerancias en comentarios
10. **FORMATO NUMÉRICO CONSISTENTE**:
    - Eliminar notación científica: `options(scipen = 999)`
    - Usar punto como separador decimal: `options(OutDec = ".")`
    - Sin separador de miles en respuestas numéricas

## 🔧 CORRECCIÓN DE ERRORES OBLIGATORIA

**ANTES de generar cualquier código, DEBES consultar los ejemplos funcionales en:**
- `Auxiliares/Ejemplos_Funcionales.md/`

**ESTOS ARCHIVOS CONTIENEN:**
- Configuraciones correctas de chunks
- Sintaxis Python corregida para matplotlib
- Manejo adecuado de reticulate
- Configuraciones LaTeX funcionales
- Estructuras de código probadas y funcionales

**PROTOCOLO DE CORRECCIÓN:**
1. Si encuentras errores de sintaxis Python → Consultar ejemplos
2. Si hay problemas con gráficos → Revisar código Python en ejemplos
3. Si falla la configuración LaTeX → Usar configuración de ejemplos
4. Si hay errores de chunks → Seguir estructuras de ejemplos
5. Si problemas con reticulate → Verificar configuración en ejemplos

**ELEMENTOS CRÍTICOS A VERIFICAR:**
- Sintaxis correcta de plt.plot() en Python
- Configuración adecuada de use_python()
- Chunks de configuración inicial completos
- Manejo correcto de variables entre R y Python
- Configuración LaTeX compatible

## 🎯 INSTRUCCIONES FINALES

Analiza la imagen proporcionada y:
1. **NUEVO:** Aplica el sistema condicional automático para detectar contenido gráfico
2. **FLUJO A o B:** Activa el flujo apropiado según detección automática
3. **Si FLUJO B:** Usa Agente-Graficador Especializado para replicación 98%+ fidelidad
4. Genera el archivo "[ejercicio]_[componente]_[competencia]_n[Nivel [1, 2, 3 o 4]]_v[versión].Rmd"
5. **PRIMERO:** Consulta los ejemplos funcionales en /Auxiliares/Ejemplos-Funcionales-Rmd/
6. Identifica el concepto matemático principal
7. Determina la competencia ICFES más apropiada
8. Diseña un problema que evalúe esa competencia
9. Genera el código .Rmd completo siguiendo EXACTAMENTE esta estructura Y los ejemplos funcionales
10. **Si FLUJO B:** Valida fidelidad visual antes de continuar con ejercicio completo
11. Asegúrate de que el ejercicio sea desafiante pero justo
12. Incluye todas las validaciones y pruebas requeridas
13. **VERIFICA** que el código siga los patrones de los ejemplos funcionales
14. Ante errores recurrentes **VERIFICA** consultando todos y cada uno de los archivos de /Auxiliares/Ejemplos-Funcionales-Rmd/

El archivo resultante debe ser completamente funcional y listo para compilar en el proyecto RepositorioMatematicasICFES_R_Exams, con replicación gráfica de alta fidelidad cuando sea necesario.

---

## 🎯 METODOLOGÍA TIKZ AVANZADA PARA REPLICACIÓN DE IMÁGENES PNG

### 📋 PROTOCOLO VALIDADO PARA NUEVAS IMÁGENES

#### **PASO 1: PREPARACIÓN**
```bash
# Colocar imagen en directorio de trabajo
/Lab/Prueba-Temporal_TikZ/nueva_imagen.png
```

#### **PASO 2: SOLICITUD ESTRUCTURADA**
Información requerida:
```
🖼️ **IMAGEN**: [nombre_archivo.png]
🎯 **OBJETIVO**: Replicar con TikZ avanzado + R-exams
📚 **CONTEXTO**: [Matemáticas/Estadística/Geometría/etc.]
📊 **NIVEL**: [ICFES Nivel 1/2/3]
🔧 **SALIDAS**: exams2html, exams2pdf, exams2moodle
```

#### **PASO 3: PROCESO AUTOMÁTICO**
1. **Análisis visual** automático de la imagen PNG
2. **Identificación** del contenido matemático específico
3. **Clasificación** del tipo de ejercicio ICFES
4. **Planificación** de estructura TikZ avanzada

#### **PASO 4: IMPLEMENTACIÓN SISTEMÁTICA**

##### **4.1 Generación TikZ Avanzada**
- Aplicar metodología TikZ con características avanzadas
- RGB colors exactos para fidelidad visual
- Posicionamiento preciso con coordenadas calculadas
- Estilos reutilizables y escalables
- Line cap round, line join round para calidad

##### **4.2 Creación .Rmd Completa**
- Estructura completa R-exams con YAML headers
- Sistema de aleatorización para 300+ versiones
- Generación de distractores pedagógicos avanzados
- Meta-información ICFES apropiada
- Integración TikZ con include_tikz()

##### **4.3 Configuración Multi-formato**
- Actualizar SemilleroUnico_v2.R automáticamente
- Configurar todos los formatos exams2*
- Verificar compatibilidad HTML/PDF/Moodle
- Resolver dependencias (magick, etc.)

##### **4.4 Validación Completa**
- Generar y probar HTML, PDF, Moodle
- Verificar fidelidad visual 98%
- Comprobar funcionalidad completa
- Documentar proceso y resultados

### 🔧 ARCHIVOS GENERADOS AUTOMÁTICAMENTE

Para cada imagen PNG:
```
📁 Lab/Prueba-Temporal_TikZ/
├── 📄 [nombre_ejercicio]_v1.Rmd          # Ejercicio principal
├── 📄 SemilleroUnico_v2.R                # Configuración actualizada
├── 📁 salida/
│   ├── 🌐 [nombre]_test.html             # Salida HTML
│   ├── 📄 [nombre]_test.pdf              # Salida PDF
│   └── 🎓 [nombre]_moodle.xml            # Salida Moodle
└── 📄 REPORTE_[NOMBRE].md                # Documentación completa
```

### ⚡ COMANDO DE ACTIVACIÓN

Para nueva imagen PNG:
> **"Aplica la metodología TikZ avanzada a esta nueva imagen PNG para generar un ejercicio R-exams completo con salidas exams2*"**

### 🎯 MÉTRICAS DE ÉXITO GARANTIZADAS

#### ✅ **Fidelidad Visual**
- **98% de similitud** con imagen original
- **Replicación exacta** de elementos matemáticos
- **Posicionamiento preciso** de todos los componentes

#### ✅ **Funcionalidad R-exams**
- **100% compatible** con sistema exams2*
- **300+ versiones** generables automáticamente
- **Aleatorización completa** de contenido

#### ✅ **Calidad Educativa ICFES**
- **Nivel apropiado** según clasificación
- **Argumentación matemática** sólida
- **Distractores pedagógicos** efectivos

### 🚀 ESTADO DE LA METODOLOGÍA

**✅ VALIDADA Y OPERATIVA**

- **Probada exitosamente**: Números triangulares (all_07.png)
- **Fidelidad comprobada**: 98% visual + 100% funcional
- **Escalabilidad confirmada**: Aplicable a cualquier imagen matemática
- **Documentación completa**: Proceso registrado y optimizado
- **Resolución automática**: Problemas comunes solucionados

**La metodología está lista para aplicar inmediatamente a cualquier nueva imagen PNG matemática.**

---

## 🤖 SISTEMA CONDICIONAL AUTOMÁTICO PARA DETECCIÓN DE CONTENIDO GRÁFICO

### 📋 PROTOCOLO DE ANÁLISIS AUTOMÁTICO

#### **PASO 1: DETECCIÓN AUTOMÁTICA DE CONTENIDO**
Al recibir una imagen PNG, el sistema analiza automáticamente:
- **Gráficas**: Barras, líneas, circulares, histogramas, dispersión, boxplots
- **Tablas**: Numéricas, textuales, mixtas, con/sin encabezados
- **Diagramas**: Matemáticos, estadísticos, geométricos, probabilidad
- **Elementos Híbridos**: Gráfica + tabla en misma imagen

#### **PASO 2: DECISIÓN DE FLUJO CONDICIONAL**
```
📷 IMAGEN PNG → 🤖 ANÁLISIS → ¿Contiene gráficas/tablas?
                                    ↓
                    ┌─────────────────────────────────┐
                    ↓                                 ↓
            🔄 FLUJO A                        🎯 FLUJO B
        (Sin gráficas complejas)          (Con gráficas/tablas)
                    ↓                                 ↓
        📋 Proceso Estándar              🤖 Agente-Graficador
           (8 Fases)                        Especializado
                    ↓                                 ↓
        ✅ Ejercicio Completo            🔄 Replicación 98%+
                                                     ↓
                                        ✅ Validación Usuario
                                                     ↓
                                        📋 Continuar 8 Fases
                                                     ↓
                                        ✅ Ejercicio Completo
```

#### **PASO 3: AGENTE-GRAFICADOR ESPECIALIZADO (Solo FLUJO B)**
- **Función**: Replicación de alta fidelidad (98%+) usando TikZ avanzado
- **Proceso Iterativo**: Refinamiento hasta alcanzar criterios de calidad
- **Templates Especializados**: Biblioteca por tipo (barras, circular, tabla)
- **Validación Obligatoria**: Aprobación usuario antes de continuar

#### **PASO 4: MÉTRICAS DE FIDELIDAD VISUAL**
- **Precisión Geométrica** (25%): Proporciones, ángulos, escalas
- **Fidelidad Cromática** (25%): Colores RGB exactos, contrastes
- **Posicionamiento** (25%): Ubicación relativa de elementos
- **Completitud** (25%): Todos los elementos presentes

### ⚡ COMANDOS DE ACTIVACIÓN

#### **Sistema Condicional Principal**
> **"Aplica el sistema condicional automático a esta imagen PNG para detectar contenido gráfico y activar el flujo apropiado"**

#### **Agente-Graficador Especializado**
> **"Activa el Agente-Graficador Especializado TikZ para replicar esta imagen con 98%+ fidelidad visual"**

#### **Validación de Fidelidad**
> **"Ejecuta la validación de fidelidad visual comparando el TikZ generado con la imagen original"**

### 🎯 INTEGRACIÓN CON METODOLOGÍAS EXISTENTES

El sistema condicional automático **EXPANDE** las metodologías existentes:
- **TikZ Avanzada**: Integrada en Agente-Graficador para replicación especializada
- **Corrección de Errores**: Aplicada en ambos flujos (A y B) durante FASE 7
- **Sistema de Distractores**: Mantenido intacto en FASE 6 para ambos flujos
- **Aleatorización 300+**: Preservada completamente en ambos flujos

**🔧 ESTADO: SISTEMA OPERATIVO Y LISTO PARA USO INMEDIATO**

---

## 🔧 METODOLOGÍA AVANZADA DE CORRECCIÓN DE ERRORES RECURRENTES

### 📋 SISTEMA DE DETECCIÓN Y CORRECCIÓN AUTOMÁTICA

#### **CATEGORÍAS DE ERRORES IDENTIFICADAS**

##### **A. ERRORES GRAMATICALES Y DE CONCORDANCIA**
```r
# ❌ INCORRECTO
"La conteo de elementos"
"Los 1 elemento"

# ✅ CORRECTO - Sistema automático de concordancia
terminos_cantidad_data <- data.frame(
  termino = c("cantidad", "número", "total", "suma", "conteo"),
  articulo = c("La", "El", "El", "La", "El"),
  stringsAsFactors = FALSE
)
```

##### **B. ERRORES DE POSICIONAMIENTO TIKZ**
```tikz
% ❌ INCORRECTO - Tabla antes que texto
% Tabla de datos
\node[anchor=north west] at (0, 1.5) {...};
% Texto explicativo
\node[anchor=north west] at (0, 0) {...};

% ✅ CORRECTO - Orden lógico
% Texto explicativo PRIMERO
\node[anchor=north west] at (0, 1.5) {...};
% Tabla DESPUÉS
\node[anchor=north west] at (0, 0.5) {...};
```

##### **C. ERRORES DE GENERACIÓN DE DATOS**
```r
# ❌ INCORRECTO - Opciones duplicadas posibles
opciones <- sample(c(respuesta, dist1, dist2, dist3), 4)

# ✅ CORRECTO - Sistema anti-duplicados
generar_opciones_unicas <- function(respuesta_correcta, num_opciones = 4) {
  # Implementación robusta que garantiza unicidad
}
```

##### **D. ERRORES DE COMPILACIÓN LATEX/TIKZ**
```r
# ❌ INCORRECTO - Paquetes insuficientes
options(tikzLatexPackages = c("\\usepackage{tikz}"))

# ✅ CORRECTO - Configuración completa
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{amsmath}",
  "\\usepackage{array}",
  "\\usepackage{xcolor}"
))
```

##### **E. ERRORES DE ESTRUCTURA R-EXAMS**
```r
# ❌ INCORRECTO - include_tikz incompleto
include_tikz(codigo, name = "fig")

# ✅ CORRECTO - Configuración completa
include_tikz(tikz_final,
             name = "nombre_descriptivo",
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl", "amsmath", "array"),
             width = "14cm")
```

### 🔍 CHECKLIST DE VALIDACIÓN RÁPIDA (2 MINUTOS)

#### **✅ Verificación Express**
- [ ] **Gramática**: ¿"El conteo" o "La cantidad"? (no "La conteo")
- [ ] **Orden TikZ**: ¿Texto → Tabla → Pregunta?
- [ ] **Opciones**: ¿4 valores diferentes?
- [ ] **Compilación**: ¿Sin errores LaTeX?
- [ ] **Visual**: ¿Tabla después del texto?

#### **🚨 Errores Críticos de Bloqueo**
1. **Concordancia de género incorrecta** (ej: "La conteo")
2. **Opciones de respuesta duplicadas**
3. **Tabla aparece antes del texto explicativo**
4. **Errores de compilación LaTeX/TikZ**
5. **Variables no definidas en chunks**

### ⚡ COMANDOS DE ACTIVACIÓN

#### **Para Corrección General**
> **"Aplica la metodología de corrección de errores recurrentes"**

#### **Para Categoría Específica**
> **"Corrige errores de concordancia de género (Categoría A)"**
> **"Corrige posicionamiento TikZ (Categoría B)"**
> **"Valida opciones únicas (Categoría C)"**

### 📚 ARCHIVOS DE REFERENCIA OBLIGATORIOS

#### **Documentación Metodológica**
- `METODOLOGIA_Correccion_Errores_Recurrentes_ICFES_R_Exams.md`
- `BIBLIOTECA_Soluciones_Errores_Comunes.md`
- `CHECKLIST_Validacion_Archivos_Rmd.md`

#### **Ejemplos Funcionales**
- **SIEMPRE consultar**: `/Auxiliares/Ejemplos-Funcionales-Rmd/`
- **Antes de cualquier corrección**: Revisar patrones probados
- **Durante corrección**: Aplicar soluciones validadas

### 🎯 INTEGRACIÓN CON METODOLOGÍA TIKZ

#### **Workflow Combinado**
1. **Consultar ejemplos funcionales** (TikZ - Fase 1)
2. **Aplicar detección de errores** (Errores - Fase 1)
3. **Corregir sistemáticamente** (Errores - Fase 4)
4. **Validar con checklist TikZ** (TikZ - Fase 6)
5. **Documentar nuevos patrones** (Ambas metodologías)

#### **Casos de Uso Integrados**
- **Replicación + Corrección**: Aplicar ambas metodologías secuencialmente
- **Optimización existente**: Priorizar corrección antes de mejoras TikZ
- **Desarrollo nuevo**: Usar ambas desde el inicio

### 📊 MÉTRICAS DE EFECTIVIDAD

#### **✅ Resultados Esperados**
- **Tiempo de corrección**: < 5 minutos para errores comunes
- **Tasa de reincidencia**: < 10% en errores ya corregidos
- **Detección automática**: > 90% de errores recurrentes
- **Calidad final**: 100% archivos sin errores críticos

**🔧 ESTADO: METODOLOGÍA VALIDADA Y OPERATIVA**

---

## 📋 PROTOCOLO ANTI-ERRORES DE IMPLEMENTACIÓN ICFES R-EXAMS

### 🎯 **METODOLOGÍA DE PREVENCIÓN SISTEMÁTICA DE ERRORES**

#### **📚 CONSULTA OBLIGATORIA PRE-IMPLEMENTACIÓN**
```
ANTES DE ESCRIBIR UNA SOLA LÍNEA DE CÓDIGO:

✅ PASO 1: Abrir `/Auxiliares/Ejemplos-Funcionales-Rmd/`
✅ PASO 2: Identificar ejemplo más similar al ejercicio objetivo
✅ PASO 3: Estudiar estructura completa del ejemplo
✅ PASO 4: Copiar configuración YAML exacta
✅ PASO 5: Copiar estructura de chunks exacta
✅ PASO 6: Identificar patrones de interpolación de variables
✅ PASO 7: Entender configuración TikZ/LaTeX específica

REGLA ABSOLUTA: "No improvises. Copia patrones probados."
```

#### **⚡ VALIDACIÓN CONTINUA DURANTE IMPLEMENTACIÓN**
```
DESPUÉS DE CADA CHUNK:

□ ¿Compiló sin errores?
□ ¿La sintaxis es idéntica al ejemplo funcional?
□ ¿Las variables R se interpolan correctamente?
□ ¿No hay caracteres extra o faltantes?

SI ALGUNA RESPUESTA ES "NO": PARAR y consultar ejemplos funcionales
```

#### **🚨 SEÑALES DE ALERTA CRÍTICAS**
```
PARAR INMEDIATAMENTE SI:

🔴 Estás interpolando variables complejas en TikZ sin ejemplo
🔴 Estás mezclando sintaxis R y LaTeX sin patrón probado
🔴 Algo "parece que debería funcionar" sin verificación
🔴 Estás improvisando configuraciones no vistas en ejemplos
🔴 Aparecen errores de compilación inesperados

ACCIÓN: Volver a ejemplos funcionales y copiar patrón exacto
```

#### **📋 CHECKLIST FINAL OBLIGATORIO**
```
ANTES DE ENTREGAR CUALQUIER .RMD:

□ ¿Consulté TODOS los ejemplos funcionales relevantes?
□ ¿La sintaxis TikZ es idéntica a ejemplos probados?
□ ¿Las variables R se interpolan correctamente?
□ ¿No hay chunks extra o caracteres sobrantes?
□ ¿La estructura completa sigue patrones probados?
□ ¿Compilación exitosa sin errores?
□ ¿Aplicé metodología de corrección de errores?
□ ¿Verifiqué funcionamiento en múltiples formatos?

SOLO ENTREGAR SI TODAS LAS RESPUESTAS SON "SÍ"
```

#### **🎯 ERRORES MÁS COMUNES IDENTIFICADOS**
1. **Interpolación incorrecta**: `\\draw[', variable, ',thick]` → `\\draw[cyan,thick]`
2. **Chunks extra**: Verificar que no sobren ``` al final
3. **Sintaxis mixta**: No mezclar R y LaTeX sin patrón probado
4. **Configuraciones inventadas**: Solo usar configuraciones de ejemplos funcionales
5. **Variables no definidas**: Verificar que todas las variables existan

#### **⚡ COMANDOS DE ACTIVACIÓN**
> **"Aplica el protocolo anti-errores de implementación"**
> **"Valida continuamente durante implementación siguiendo ejemplos funcionales"**

### 🎯 **INTEGRACIÓN CON METODOLOGÍAS EXISTENTES**

El protocolo anti-errores se **INTEGRA COMPLETAMENTE** con:
- **Sistema Condicional Automático**: Validación durante FLUJO A y B
- **Metodología TikZ Avanzada**: Prevención de errores de interpolación
- **Corrección de Errores Recurrentes**: Aplicación durante implementación (no solo al final)

### 📊 **MÉTRICAS DE EFECTIVIDAD ESPERADAS**
- **Tiempo de corrección**: < 5 minutos para errores comunes
- **Tasa de reincidencia**: < 5% en errores ya identificados
- **Detección preventiva**: > 95% de errores antes de que ocurran
- **Calidad final**: 100% archivos sin errores críticos de implementación

**🔧 ESTADO: PROTOCOLO VALIDADO Y OPERATIVO**

**Todas las metodologías (Sistema Condicional + TikZ + Corrección de Errores + Protocolo Anti-Errores) están integradas y listas para uso inmediato en cualquier archivo .Rmd del proyecto ICFES.**
