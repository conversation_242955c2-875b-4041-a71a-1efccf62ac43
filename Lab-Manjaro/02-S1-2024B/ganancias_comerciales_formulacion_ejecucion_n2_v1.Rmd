---
output:
  html_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]
  word_document: default
icfes:
  competencia: formulacion_ejecucion
  componente: numerico_variacional
  afirmacion: Resuelve problemas que requieren el planteamiento y estructuración de soluciones ante contextos problémicos
  evidencia: Utiliza operaciones básicas para resolver problemas de aplicación comercial
  nivel: 2
  tematica: Operaciones básicas y aplicaciones comerciales
  contexto: laboral
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")
options(scipen = 999)
options(digits = 10)

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

# Librerías esenciales
library(exams)
library(digest)
library(testthat)

typ <- match_exams_device()
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)
```

```{r generar_datos, message=FALSE, warning=FALSE, results='asis'}
# Configuración simplificada
options(scipen = 999, OutDec = ".")

# Semilla aleatoria para diversidad
set.seed(as.numeric(Sys.time()) + sample(1:10000, 1))

# Función para formatear números enteros sin notación científica
formatear_entero <- function(numero) {
  formatC(numero, format = "d", big.mark = "")
}

# Aleatorización simplificada del contexto comercial
contextos_data <- data.frame(
  nombre = c("tienda", "empresa", "negocio"),
  genero = c("f", "f", "m"),
  articulo = c("una", "una", "un"),
  stringsAsFactors = FALSE
)
contexto_seleccionado <- contextos_data[sample(nrow(contextos_data), 1), ]
contexto <- contexto_seleccionado$nombre
articulo_contexto <- contexto_seleccionado$articulo

# Aleatorización simplificada del tipo de comerciante
comerciantes_data <- data.frame(
  nombre = c("vendedor", "empresario", "comerciante"),
  genero = c("m", "m", "m"),
  articulo_el = c("el", "el", "el"),
  articulo_un = c("un", "un", "un"),
  stringsAsFactors = FALSE
)
comerciante_seleccionado <- comerciantes_data[sample(nrow(comerciantes_data), 1), ]
comerciante <- comerciante_seleccionado$nombre
articulo_el_comerciante <- comerciante_seleccionado$articulo_el
articulo_un_comerciante <- comerciante_seleccionado$articulo_un

# Aleatorización simplificada del producto
productos_data <- data.frame(
  plural = c("pantalones", "camisas", "zapatos"),
  singular = c("pantalón", "camisa", "zapato"),
  genero = c("m", "f", "m"),
  stringsAsFactors = FALSE
)
producto_seleccionado <- productos_data[sample(nrow(productos_data), 1), ]
producto <- producto_seleccionado$plural
producto_singular <- producto_seleccionado$singular
genero_producto <- producto_seleccionado$genero

# Aleatorización matemáticamente relevante de precios
# Diferentes rangos de ganancia para crear diversidad real en el problema
tipo_ganancia <- sample(c("baja", "media", "alta"), 1)

if (tipo_ganancia == "baja") {
  precio_compra <- sample(c(80, 90, 100, 120), 1) * 1000
  margen_ganancia <- sample(c(0.10, 0.15, 0.20), 1)  # 10-20% ganancia
} else if (tipo_ganancia == "media") {
  precio_compra <- sample(c(60, 70, 80, 100), 1) * 1000
  margen_ganancia <- sample(c(0.25, 0.30, 0.35), 1)  # 25-35% ganancia
} else {
  precio_compra <- sample(c(50, 60, 70, 80), 1) * 1000
  margen_ganancia <- sample(c(0.40, 0.50, 0.60), 1)  # 40-60% ganancia
}

# Calcular precio de venta basado en el margen
precio_venta <- precio_compra * (1 + margen_ganancia)
ganancia_unitaria <- precio_venta - precio_compra

# Número de unidades vendidas (matemáticamente relevante)
unidades_vendidas <- sample(c(5, 8, 10, 12, 15, 20, 25), 1)
ganancia_total <- ganancia_unitaria * unidades_vendidas

# Aleatorización simplificada de términos clave
terminos_desea <- c("desea", "quiere", "necesita")
termino_desea <- sample(terminos_desea, 1)

terminos_conocer <- c("conocer", "determinar", "calcular")
termino_conocer <- sample(terminos_conocer, 1)

terminos_obtiene <- c("obtiene", "consigue", "logra")
termino_obtiene <- sample(terminos_obtiene, 1)

# Términos simplificados (usando solo masculino para simplificar)
termino_venta <- "venta"
articulo_venta <- "la"

termino_compra <- sample(c("compra", "adquiere", "obtiene"), 1)

termino_procedimientos <- sample(c("procedimientos", "métodos", "procesos"), 1)

termino_permite <- sample(c("permite", "facilita", "posibilita"), 1)

# Término de monto simplificado
termino_monto <- "monto"
articulo_monto <- "el"

# Generar opciones de respuesta con sistema avanzado de distractores
# La respuesta correcta es la opción A del problema original

# SISTEMA AVANZADO DE DISTRACTORES
# 30% probabilidad de incluir valores duplicados con justificaciones diferentes
permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))

# Respuestas correctas enfocadas en el procedimiento (no en valores específicos)
respuestas_correctas <- c(
  paste0("Restar $", formatear_entero(precio_compra),
         " al precio de venta de cada ", producto_singular,
         " y multiplicar dicho valor por el número de unidades vendidas."),

  paste0("Calcular la diferencia entre el precio de venta y $", formatear_entero(precio_compra),
         " por cada ", producto_singular,
         ", y multiplicar este resultado por la cantidad de unidades vendidas."),

  paste0("Al precio de venta unitario de cada ", producto_singular,
         " restarle $", formatear_entero(precio_compra),
         " y multiplicar la diferencia obtenida por el total de unidades vendidas."),

  paste0("Determinar la ganancia por ", producto_singular,
         " restando $", formatear_entero(precio_compra),
         " del precio de venta, y multiplicar por el número de unidades comercializadas."),

  paste0("Obtener la utilidad unitaria sustrayendo $", formatear_entero(precio_compra),
         " del precio de venta de cada ", producto_singular,
         ", luego multiplicar por la cantidad total vendida.")
)

# Seleccionar aleatoriamente una de las 5 respuestas correctas
afirmacion_correcta <- sample(respuestas_correctas, 1)

# Distractores con errores conceptuales comunes
# Distractor B: Error de inversión de la resta (precio_compra - precio_venta)
distractores_b <- c(
  paste0("A $", formatear_entero(precio_compra),
         " restarle el precio de venta de cada ", producto_singular,
         " y multiplicar dicho valor por el número de unidades vendidas."),

  paste0("Calcular la diferencia entre $", formatear_entero(precio_compra),
         " y el precio de venta de cada ", producto_singular,
         ", y multiplicar este resultado por la cantidad de unidades vendidas."),

  paste0("Del costo de $", formatear_entero(precio_compra),
         " sustraer el precio de venta de cada ", producto_singular,
         " y multiplicar la diferencia por el total de unidades vendidas."),

  paste0("Determinar cuánto excede $", formatear_entero(precio_compra),
         " al precio de venta de cada ", producto_singular,
         " y multiplicar por el número de unidades comercializadas."),

  paste0("Obtener la diferencia restando el precio de venta de cada ", producto_singular,
         " a $", formatear_entero(precio_compra),
         ", luego multiplicar por la cantidad total vendida.")
)

# Seleccionar aleatoriamente una de las 5 variaciones del distractor B
distractor_b <- sample(distractores_b, 1)

# Distractor C: Error de confundir ganancia total con ganancia unitaria
distractor_c <- paste0("Multiplicar el precio de venta de cada ", producto_singular,
                      " por el número de unidades vendidas y restar $", formatear_entero(precio_compra), ".")

# Distractor D: Error de usar solo el precio de compra como ganancia
distractor_d <- paste0("Multiplicar $", formatear_entero(precio_compra),
                      " por el número de unidades vendidas para obtener la ganancia total.")

# Crear vector con todas las opciones
opciones_originales <- c(afirmacion_correcta, distractor_b, distractor_c, distractor_d)
names(opciones_originales) <- c("correcta", "distractor_b", "distractor_c", "distractor_d")

# Aleatorizar manualmente las opciones
indices_mezclados <- sample(1:4)
opciones <- opciones_originales[indices_mezclados]

# Encontrar la nueva posición de la respuesta correcta
posicion_correcta <- which(indices_mezclados == 1)

# Crear el vector de solución
solucion <- rep(0, 4)
solucion[posicion_correcta] <- 1

# Pruebas de validación matemática
test_that("Validaciones matemáticas optimizadas", {
  # Verificar coherencia matemática
  expect_true(precio_compra > 0 && precio_venta > 0,
              info = "Los precios deben ser positivos")

  expect_true(precio_venta > precio_compra,
              info = "El precio de venta debe ser mayor al de compra para tener ganancia")

  expect_equal(ganancia_unitaria, precio_venta - precio_compra,
              info = "La ganancia unitaria debe calcularse correctamente")

  expect_equal(ganancia_total, ganancia_unitaria * unidades_vendidas,
              info = "La ganancia total debe calcularse correctamente")

  # Verificar diversidad de opciones
  expect_equal(length(unique(opciones)), 4,
               info = "Las 4 opciones deben ser textualmente diferentes")

  # Verificar respuesta correcta
  expect_true(afirmacion_correcta %in% opciones,
              info = "La respuesta correcta debe estar presente")

  # Verificar vector de solución
  expect_equal(sum(solucion), 1,
              info = "Debe haber exactamente una respuesta correcta")
})

# Prueba de diversidad de versiones (estándar del proyecto: 300+ versiones)
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    set.seed(i)
    # Simular generación matemáticamente relevante
    tipo_ganancia_test <- sample(c("baja", "media", "alta"), 1)
    contexto_test <- sample(contextos_data$nombre, 1)
    comerciante_test <- sample(comerciantes_data$nombre, 1)
    producto_test <- sample(productos_data$plural, 1)
    unidades_test <- sample(c(5, 8, 10, 12, 15, 20, 25), 1)

    # Simular precios según tipo de ganancia
    if (tipo_ganancia_test == "baja") {
      precio_compra_test <- sample(c(80, 90, 100, 120), 1) * 1000
      margen_test <- sample(c(0.10, 0.15, 0.20), 1)
    } else if (tipo_ganancia_test == "media") {
      precio_compra_test <- sample(c(60, 70, 80, 100), 1) * 1000
      margen_test <- sample(c(0.25, 0.30, 0.35), 1)
    } else {
      precio_compra_test <- sample(c(50, 60, 70, 80), 1) * 1000
      margen_test <- sample(c(0.40, 0.50, 0.60), 1)
    }

    precio_venta_test <- precio_compra_test * (1 + margen_test)

    datos_test <- list(tipo_ganancia = tipo_ganancia_test, contexto = contexto_test,
                      comerciante = comerciante_test, producto = producto_test,
                      precio_compra = precio_compra_test, precio_venta = precio_venta_test,
                      unidades = unidades_test, margen = margen_test)
    versiones[[i]] <- digest::digest(datos_test)
  }

  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas de 1000 intentos. Se requieren al menos 300."))
})
```

Question
========

Un `r comerciante` `r termino_desea` `r termino_conocer` el valor de las ganancias que `r termino_obtiene` por la venta de `r producto`.

En este sentido, si el `r comerciante` `r termino_compra` cada `r producto_singular` a $`r formatear_entero(precio_compra)`, ¿cuál de los siguientes `r termino_procedimientos` le `r termino_permite` determinar el `r termino_monto` de sus ganancias?

Answerlist
----------
- `r opciones[1]`
- `r opciones[2]`
- `r opciones[3]`
- `r opciones[4]`

Solution
========

Para resolver este problema, debemos entender el concepto de ganancia en el contexto comercial y identificar el procedimiento correcto para calcularla.

### Concepto de ganancia

La **ganancia** en una transacción comercial se define como:

**Ganancia = Precio de venta - Precio de compra**

### Análisis del problema

Datos del problema:

- Precio de compra por unidad: $`r formatear_entero(precio_compra)`
- Se busca determinar el procedimiento para calcular las ganancias totales

### Procedimiento correcto

Para calcular las ganancias totales por la venta de `r producto`, debemos:

1. **Calcular la ganancia por unidad**: Precio de venta - Precio de compra
2. **Multiplicar por el número de unidades vendidas**: (Precio de venta - Precio de compra) × Número de unidades

Esto es equivalente a:

**(Precio de venta - $`r formatear_entero(precio_compra)`) × Número de unidades vendidas**

### Análisis de las opciones

**Opción correcta**: 

"`r afirmacion_correcta`"

- Esta opción representa correctamente la fórmula: (Precio de venta - Precio de compra) × Cantidad
- Primero resta el costo al precio de venta para obtener la ganancia unitaria
- Luego multiplica por las unidades vendidas para obtener la ganancia total

**Análisis de distractores**:

- Las demás opciones presentan errores en el orden de las operaciones o en la interpretación del concepto de ganancia
- Algunos invierten la resta (precio de compra - precio de venta), lo que daría pérdidas en lugar de ganancias
- Otros alteran el orden de las operaciones, llevando a cálculos incorrectos

**Por lo tanto, la respuesta correcta es la que establece restar el precio de compra al precio de venta y multiplicar por las unidades vendidas.**

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: ganancias_comerciales_formulacion_ejecucion
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: FALSE
exsection: Aritmética|Operaciones básicas|Aplicaciones comerciales
