```{r data_generation, echo = FALSE, results = "hide"}
# Configuracion inicial
Sys.setlocale("LC_ALL", "C")
options(OutDec = ".")
options(scipen = 999)
options(digits = 10)

# Librerias esenciales
library(exams)

# Semilla aleatoria
set.seed(sample(1:100000, 1))

# Contextos variados para la pregunta
contextos <- list(
  list(
    materia = "Matematicas",
    actividad = "examenes",
    periodo = "bimestre",
    estudiante = sample(c("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"), 1)
  ),
  list(
    materia = "Ciencias Naturales", 
    actividad = "laboratorios",
    periodo = "trimestre",
    estudiante = sample(c("Cam<PERSON>", "Andres", "Valentina", "Santiago", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"), 1)
  ),
  list(
    materia = "Espanol",
    actividad = "ensayos",
    periodo = "periodo academico",
    estudiante = sample(c("<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"), 1)
  )
)

contexto_seleccionado <- sample(contextos, 1)[[1]]

# <PERSON>rar numero de calificaciones (entre 4 y 6)
num_calificaciones <- sample(4:6, 1)

# Generar calificaciones realistas (entre 2.5 y 5.0)
calificaciones <- round(runif(num_calificaciones, 2.5, 5.0), 1)

# Calcular la media aritmetica correcta
media_correcta <- round(mean(calificaciones), 1)
suma_total <- sum(calificaciones)

# Generar distractores diversos
distractores_valores <- c()

# Distractor 1: Confundir con mediana
mediana_calculada <- round(median(calificaciones), 1)
if(mediana_calculada != media_correcta) {
  distractores_valores <- c(distractores_valores, mediana_calculada)
}

# Distractor 2: Usar solo la primera y ultima calificacion
promedio_extremos <- round((calificaciones[1] + calificaciones[length(calificaciones)]) / 2, 1)
if(promedio_extremos != media_correcta) {
  distractores_valores <- c(distractores_valores, promedio_extremos)
}

# Distractor 3: Valor maximo
valor_maximo <- max(calificaciones)
if(valor_maximo != media_correcta) {
  distractores_valores <- c(distractores_valores, valor_maximo)
}

# Completar con valores aleatorios si no hay suficientes
while(length(distractores_valores) < 3) {
  nuevo_valor <- round(runif(1, 2.0, 5.0), 1)
  if(!(nuevo_valor %in% c(distractores_valores, media_correcta))) {
    distractores_valores <- c(distractores_valores, nuevo_valor)
  }
}

# Seleccionar 3 distractores unicos
distractores_seleccionados <- unique(distractores_valores)[1:3]

# Crear opciones con justificaciones
opciones <- c()

# Opcion correcta
opciones[1] <- paste0(media_correcta, 
                     " porque se suman todas las calificaciones y se divide por el numero total de evaluaciones")

# Distractores con justificaciones incorrectas
opciones[2] <- paste0(distractores_seleccionados[1],
                     " porque representa el valor central de las calificaciones ordenadas")

opciones[3] <- paste0(distractores_seleccionados[2],
                     " porque es el resultado de sumar todas las calificaciones")

opciones[4] <- paste0(distractores_seleccionados[3],
                     " porque es el promedio entre la calificacion mas alta y la mas baja")

# Determinar respuesta correcta y mezclar opciones
solutions <- c(TRUE, FALSE, FALSE, FALSE)
orden_aleatorio <- sample(1:4)
opciones <- opciones[orden_aleatorio]
solutions <- solutions[orden_aleatorio]
```

Question
========

En el `r contexto_seleccionado$periodo` de `r contexto_seleccionado$materia`, `r contexto_seleccionado$estudiante` obtuvo las siguientes calificaciones en `r num_calificaciones` `r contexto_seleccionado$actividad`:

`r paste(calificaciones, collapse = ", ")`

¿Cual es la media aritmetica de estas calificaciones?

```{r questionlist, echo = FALSE, results = "asis"}
answerlist(opciones, markup = "markdown")
```

Solution
========

Para calcular la media aritmetica de las calificaciones de `r contexto_seleccionado$estudiante`, debemos:

1. Sumar todas las calificaciones: `r paste(calificaciones, collapse = " + ")` = `r suma_total`

2. Dividir la suma por el numero de calificaciones: `r suma_total` / `r num_calificaciones` = `r media_correcta`

Por lo tanto, la media aritmetica es `r media_correcta`.

```{r solutionlist, echo = FALSE, results = "asis"}
answerlist(ifelse(solutions, "Correcto", "Incorrecto"), markup = "markdown")
```

Meta-information
================
extype: schoice
exsolution: `r mchoice2string(solutions, single = TRUE)`
exname: Media Aritmetica Calificaciones
exsection: Estadistica
