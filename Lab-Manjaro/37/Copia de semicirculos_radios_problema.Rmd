---
encoding: UTF-8
output:
  html_document: default
  word_document: default
  pdf_document: default
---
encoding: UTF-8
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usetikzlibrary{calc,patterns,angles,quotes,arrows.meta}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# Aleatorización de parámetros del problema
# Generar valores para el segmento AC (entre 9 y 15 cm, múltiplos de 0.5)
ac_opciones <- seq(9, 15, by = 0.5)
ac_valor <- sample(ac_opciones, 1)

# Aleatorizar la proporción de longitudes (BP:BC)
proporciones <- c("1:2", "2:3", "1:3", "3:4", "2:5", "1:4")
proporcion <- sample(proporciones, 1)

# Extraer los valores numéricos de la proporción
prop_nums <- as.numeric(strsplit(proporcion, ":")[[1]])
bp_ratio <- prop_nums[1] / sum(prop_nums)
bc_valor <- ac_valor - ac_valor/2  # BC = AC - AB = AC - AC/2 = AC/2

# Calcular BP basado en la proporción
bp_valor <- bc_valor * bp_ratio
pc_valor <- bc_valor - bp_valor

# Determinar la variable X para el esquema (donde 2X + 2X + X + X = ac_valor)
x_valor <- ac_valor / 6

# Determinar los radios basados en las proporciones de segmentos
# Para el primer semicírculo (centro en O)
radio_o <- ac_valor / 2

# Para el segundo semicírculo (centro en P)
radio_p <- pc_valor  # El radio es la distancia de P a C

# Redondear los valores a 1 decimal si son decimales, o a enteros si son exactos
radio_o_redondeado <- ifelse(radio_o %% 1 == 0, as.integer(radio_o), round(radio_o, 1))
radio_p_redondeado <- ifelse(radio_p %% 1 == 0, as.integer(radio_p), round(radio_p, 1))

# Aleatorizar para determinar si se muestran los radios con unidades
mostrar_unidades <- sample(c(TRUE, FALSE), 1)
unidad_longitud <- sample(c("cm", "m", "mm", "dm"), 1)

# Formatear los radios para presentación
radio_o_texto <- ifelse(mostrar_unidades, 
                        paste(radio_o_redondeado, unidad_longitud), 
                        as.character(radio_o_redondeado))
radio_p_texto <- ifelse(mostrar_unidades, 
                        paste(radio_p_redondeado, unidad_longitud), 
                        as.character(radio_p_redondeado))

# Aleatorizar el orden de los radios en las opciones de respuesta
primer_radio <- ifelse(sample(c(TRUE, FALSE), 1), radio_o_redondeado, radio_p_redondeado)
segundo_radio <- ifelse(primer_radio == radio_o_redondeado, radio_p_redondeado, radio_o_redondeado)

# Crear distractores creíbles para las respuestas
# Distractor 1: Duplicar ambos radios
distractor1_r1 <- primer_radio * 2
distractor1_r2 <- segundo_radio * 2

# Distractor 2: Multiplicar un radio por 2 y el otro por 0.5
distractor2_r1 <- primer_radio * 2
distractor2_r2 <- segundo_radio * 0.5

# Distractor 3: Invertir los radios y redondear
distractor3_r1 <- round(ac_valor - primer_radio)
distractor3_r2 <- round(ac_valor - segundo_radio)

# Formatear todas las opciones con unidades si es necesario
format_radio <- function(r) {
  r_display <- ifelse(r %% 1 == 0, as.integer(r), round(r, 1))
  ifelse(mostrar_unidades, paste(r_display, unidad_longitud), as.character(r_display))
}

opcion_correcta <- paste(format_radio(radio_o_redondeado), "y", format_radio(radio_p_redondeado), "respectivamente")
distractor1 <- paste(format_radio(distractor1_r1), "y", format_radio(distractor1_r2), "respectivamente")
distractor2 <- paste(format_radio(distractor2_r1), "y", format_radio(distractor2_r2), "respectivamente")
distractor3 <- paste(format_radio(distractor3_r1), "y", format_radio(distractor3_r2), "respectivamente")

# Aleatorizar el orden de las opciones de respuesta
opciones <- c(opcion_correcta, distractor1, distractor2, distractor3)
indice_correcto <- 1  # El índice de la respuesta correcta en el vector original

# Barajar las opciones
set.seed(sample(1:10000, 1))  # Nueva semilla para barajar
indice_barajado <- sample(1:4)
opciones_barajadas <- opciones[indice_barajado]
# Actualizar el índice de la respuesta correcta después de barajar
nuevo_indice_correcto <- which(indice_barajado == indice_correcto)

# Vector de solución para r-exams (formato binario para schoice)
solucion <- rep(0,4)
solucion[nuevo_indice_correcto] <- 1

# Para personalizar el problema
# Aleatorizar nombres para los personajes
nombres_masculinos <- c("Andrés", "Carlos", "David", "Eduardo", "Fernando", "Gabriel", 
                       "Héctor", "Ignacio", "Javier", "Luis", "Miguel", "Óscar", 
                       "Pablo", "Raúl", "Santiago", "Tomás", "Vicente", "Walter")
nombres_femeninos <- c("Ana", "Beatriz", "Carmen", "Diana", "Elena", "Fernanda", 
                      "Gloria", "Helena", "Isabel", "Julia", "Karen", "Laura", 
                      "María", "Natalia", "Olga", "Patricia", "Rosa", "Silvia")
nombre_estudiante <- sample(c(sample(nombres_masculinos, 1), sample(nombres_femeninos, 1)), 1)

# Aleatorizar contextos para el problema
contextos <- c(
  "diseño de un jardín semicircular",
  "construcción de un escenario para una presentación",
  "diseño de una piscina con forma de semicírculo",
  "elaboración de un espejo decorativo",
  "diseño de un parque infantil",
  "construcción de una tarima para un evento",
  "diseño de una fuente ornamental",
  "creación de un modelo arquitectónico",
  "elaboración de un proyecto de ingeniería",
  "diseño de un estanque decorativo"
)
contexto <- sample(contextos, 1)

# Aleatorizar verbos y términos
verbos_calculo <- c("calcular", "determinar", "hallar", "encontrar", "obtener")
verbo_calculo <- sample(verbos_calculo, 1)

terminos_problema <- c("problema", "ejercicio", "desafío", "reto", "situación")
termino_problema <- sample(terminos_problema, 1)

terminos_figura <- c("figura", "imagen", "ilustración", "diagrama", "esquema")
termino_figura <- sample(terminos_figura, 1)

# Aleatorización de colores para los diagramas
colores_lineas <- c("black", "blue", "red", "purple", "green!70!black", "orange!80!black")
color_lineas <- sample(colores_lineas, 1)
colores_puntos <- c("black", "blue", "red", "purple", "green", "orange")
color_puntos <- sample(colores_puntos, 1)
colores_texto <- c("black", "blue!70!black", "red!70!black", "purple!70!black", "green!50!black", "orange!70!black")
color_texto <- sample(colores_texto, 1)

# Estilo del diagrama
estilos_linea <- c("thick", "very thick", "semithick")
estilo_linea <- sample(estilos_linea, 1)
```

```{r generar_diagrama_tikz_esquema, echo=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Función para generar el diagrama TikZ del esquema con las divisiones
generar_esquema_tikz <- function(ac_valor, radio_o, radio_p, bp_valor, pc_valor, 
                                 color_lineas, color_puntos, color_texto, estilo_linea, x_valor) {
  
  # Calcular las coordenadas de los puntos
  punto_a <- c(0,0)
  punto_c <- c(ac_valor, 0)
  punto_o <- c(ac_valor/2, 0)
  punto_b <- c(punto_o[[1]] + radio_o, 0)
  punto_p <- c(punto_b[[1]] + bp_valor, 0)
  
  # Crear el código TikZ para el esquema
  esquema_code <- c(
    "\\begin{tikzpicture}[scale=0.6]",
    # Dibujar el segmento AC con etiqueta de longitud
    paste0("  \\draw[", estilo_linea, ", ", color_lineas, "] (", punto_a[1], ",0) -- (", punto_c[1], ",0);"),
    paste0("  \\draw[dashed] (", punto_a[1], ",0) -- (", punto_a[1], ",", 2*radio_o, ");"),
    paste0("  \\draw[dashed] (", punto_o[1], ",0) -- (", punto_o[1], ",", 2*radio_o, ");"),
    paste0("  \\draw[dashed] (", punto_b[1], ",0) -- (", punto_b[1], ",", 2*radio_o, ");"),
    paste0("  \\draw[dashed] (", punto_p[1], ",0) -- (", punto_p[1], ",", 2*radio_o, ");"),
    paste0("  \\draw[dashed] (", punto_c[1], ",0) -- (", punto_c[1], ",", 2*radio_o, ");"),
    # Etiqueta para la longitud total
    paste0("  \\draw[<->] (", punto_a[1], ",", radio_o*1.5, ") -- (", punto_c[1], ",", radio_o*1.5, 
           ") node[midway, above] {\\textbf{", ac_valor, " ", unidad_longitud, "}};"),
    # Etiquetas para las divisiones (2X, 2X, X, X)
    paste0("  \\draw[<->] (", punto_a[1], ",", radio_o*1.2, ") -- (", punto_o[1], ",", radio_o*1.2,
           ") node[midway, above] {\\textbf{2X ", unidad_longitud, "}};"),
    paste0("  \\draw[<->] (", punto_o[1], ",", radio_o*1.2, ") -- (", punto_b[1], ",", radio_o*1.2,
           ") node[midway, above] {\\textbf{2X ", unidad_longitud, "}};"),
    paste0("  \\draw[<->] (", punto_b[1], ",", radio_o*1.2, ") -- (", punto_p[1], ",", radio_o*1.2,
           ") node[midway, above] {\\textbf{X ", unidad_longitud, "}};"),
    paste0("  \\draw[<->] (", punto_p[1], ",", radio_o*1.2, ") -- (", punto_c[1], ",", radio_o*1.2,
           ") node[midway, above] {\\textbf{X ", unidad_longitud, "}};"),
    # Dibujar los semicírculos (usar arc para dibujarlos)
    paste0("  \\draw[", estilo_linea, ", ", color_lineas, "] (", punto_o[1] - radio_o, ",0) arc (180:0:", radio_o, ");"),
    paste0("  \\draw[", estilo_linea, ", ", color_lineas, "] (", punto_p[1] - radio_p, ",0) arc (180:0:", radio_p, ");"),
    # Añadir puntos etiquetados A, O, B, P, C
    paste0("  \\node[", color_texto, ", below] at (", punto_a[1], ",0) {\\textbf{A}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_o[1], ",0) {\\textbf{O}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_b[1], ",0) {\\textbf{B}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_p[1], ",0) {\\textbf{P}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_c[1], ",0) {\\textbf{C}};"),
    "\\end{tikzpicture}"
  )
  
  return(esquema_code)
}

# Generar el diagrama TikZ para el problema
esquema_tikz <- generar_esquema_tikz(ac_valor, radio_o, radio_p, bp_valor, pc_valor, 
                                     color_lineas, color_puntos, color_texto, estilo_linea, x_valor)
```

```{r generar_diagrama_tikz_inicial, echo=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Función para generar el diagrama TikZ inicial con las medidas
generar_diagrama_inicial_tikz <- function(ac_valor, bp_valor, radio_o, radio_p, color_lineas, color_puntos, color_texto, estilo_linea) {
  
  # Calcular las coordenadas de los puntos
  punto_a <- c(0,0)
  punto_c <- c(ac_valor, 0)
  punto_o <- c(ac_valor/2, 0)  # O está en el medio de AC
  punto_b <- c(punto_o[[1]] + radio_o, 0)
  punto_p <- c(punto_b[[1]] + bp_valor, 0)
  
  # Crear el código TikZ para el diagrama inicial
  diagrama_code <- c(
    "\\begin{tikzpicture}[scale=0.7]",
    # Dibujar los semicírculos
    paste0("  \\draw[", estilo_linea, ", ", color_lineas, "] (", punto_o[1] - radio_o, ",0) arc (180:0:", radio_o, ");"),
    paste0("  \\draw[", estilo_linea, ", ", color_lineas, "] (", punto_p[1] - radio_p, ",0) arc (180:0:", radio_p, ");"),
    # Dibujar el segmento AC
    paste0("  \\draw[", estilo_linea, ", ", color_lineas, "] (", punto_a[1], ",0) -- (", punto_c[1], ",0);"),
    # Añadir puntos etiquetados A, O, B, P, C
    paste0("  \\node[", color_texto, ", below] at (", punto_a[1], ",0) {\\textbf{A}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_o[1], ",0) {\\textbf{O}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_b[1], ",0) {\\textbf{B}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_p[1], ",0) {\\textbf{P}};"),
    paste0("  \\node[", color_texto, ", below] at (", punto_c[1], ",0) {\\textbf{C}};"),
    "\\end{tikzpicture}"
  )
  
  return(diagrama_code)
}

# Generar el diagrama TikZ inicial
diagrama_inicial_tikz <- generar_diagrama_inicial_tikz(ac_valor, bp_valor, radio_o, radio_p, color_lineas, color_puntos, color_texto, estilo_linea)
```

```{r generar_calculo_python, echo=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Vamos a evitar LaTeX por completo y usar un enfoque más sencillo
codigo_python <- paste0("
import matplotlib.pyplot as plt
import numpy as np
import matplotlib

# Configuración general
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['text.usetex'] = False  # Desactivar LaTeX

# Crear una figura
fig, ax = plt.subplots(figsize=(6,3))
ax.axis('off')

# Datos del problema
ac_valor = ", ac_valor, "
x_valor = ac_valor / 6  # X en el esquema donde 2X + 2X + X + X = ac_valor

# Textos simplificados sin LaTeX
equation1 = 'Planteamiento:'
equation2 = f'2X + 2X + X + X = {ac_valor} ", unidad_longitud, "'
equation3 = f'6X = {ac_valor} ", unidad_longitud, "'
equation4 = f'X = {ac_valor}/6 = {round(x_valor, 2)} ", unidad_longitud, "'
equation5 = f'Radio de O = 2X = 2 × {round(x_valor, 2)} = {", radio_o_redondeado, "} ", unidad_longitud, "'
equation6 = f'Radio de P = X = {round(x_valor, 2)} = {", radio_p_redondeado, "} ", unidad_longitud, "'

# Añadir los textos al gráfico
ax.text(0.05, 0.9, equation1, fontsize=12, fontweight='bold')
ax.text(0.05, 0.75, equation2, fontsize=12)
ax.text(0.05, 0.6, equation3, fontsize=12)
ax.text(0.05, 0.45, equation4, fontsize=12)
ax.text(0.05, 0.3, equation5, fontsize=12)
ax.text(0.05, 0.15, equation6, fontsize=12)

# Guardar figura
plt.savefig('ecuaciones.png', dpi=150, bbox_inches='tight')
plt.close()
")

# Ejecutar el código Python para generar la imagen de las ecuaciones
py_run_string(codigo_python)
```

Question
========

En la `r termino_figura` adjunta, AC = `r ac_valor` `r unidad_longitud` y AO = `r ac_valor/2` `r unidad_longitud`.

```{r diagrama_inicial, echo=FALSE, results='asis'}
include_tikz(diagrama_inicial_tikz, 
             name = "diagrama_inicial", 
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl"),
             width = "8cm")
```

Se desea `r verbo_calculo` los radios de los dos semicírculos de centro O y P. Para resolver este `r termino_problema`, `r nombre_estudiante` ha planteado el siguiente esquema.

```{r esquema, echo=FALSE, results='asis'}
include_tikz(esquema_tikz, 
             name = "esquema_semicirculos", 
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl"),
             width = "10cm")
```

¿Cuáles son las medidas de los radios de los dos semicírculos?

Answerlist
----------
encoding: UTF-8
- `r opciones_barajadas[1]`
- `r opciones_barajadas[2]`
- `r opciones_barajadas[3]`
- `r opciones_barajadas[4]`

Solution
========

Para resolver este problema, debemos analizar la distribución de los puntos en el eje horizontal y encontrar los radios de ambos semicírculos:

```{r solucion, echo=FALSE, results='asis', fig.align='center'}
cat("![](ecuaciones.png)")
```

El esquema divide la longitud total AC = `r ac_valor` `r unidad_longitud` en segmentos proporcionales a 2X, 2X, X y X.

Sabemos que:
- AC = `r ac_valor` `r unidad_longitud`
- 2X + 2X + X + X = `r ac_valor` `r unidad_longitud`
- 6X = `r ac_valor` `r unidad_longitud`
- X = `r ac_valor`/6 = `r round(ac_valor/6, 2)` `r unidad_longitud`

Por lo tanto:
- El radio del semicírculo con centro en O es 2X = `r radio_o_redondeado` `r unidad_longitud`
- El radio del semicírculo con centro en P es X = `r radio_p_redondeado` `r unidad_longitud`

La respuesta correcta es `r radio_o_redondeado` `r if(mostrar_unidades) unidad_longitud` y `r radio_p_redondeado` `r if(mostrar_unidades) unidad_longitud`, respectivamente.

Answerlist
----------
encoding: UTF-8
- `r ifelse(solucion[1] == 1, "Verdadero", "Falso")`
- `r ifelse(solucion[2] == 1, "Verdadero", "Falso")`
- `r ifelse(solucion[3] == 1, "Verdadero", "Falso")`
- `r ifelse(solucion[4] == 1, "Verdadero", "Falso")`

Meta-information
================
exname: radios_semicirculos
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Geometría
