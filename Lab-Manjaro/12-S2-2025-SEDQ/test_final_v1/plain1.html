<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 1</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 1</h2>

<ol>
<li>
<h4>Question</h4>
<p>cierta especie de aves aumenta su población según la función <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mi>k</mi><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn><mo>⋅</mo><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>3</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mo>⋅</mo><mi>k</mi></mrow></msup></mrow><annotation encoding="application/x-tex">r(k) = 50 \cdot (3)^{0.4 \cdot k}</annotation></semantics></math>, donde <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mi>k</mi><annotation encoding="application/x-tex">k</annotation></semantics></math> se mide en minutos y <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mi>r</mi><annotation encoding="application/x-tex">r</annotation></semantics></math> es la cantidad de aves.</p>
<p>De acuerdo con la información anterior, la cantidad de aves que había al iniciar un monitoreo ecológico, es:</p>
<br/>
<ol type="a">
<li>
53 aves.
</li>
<li>
3 aves.
</li>
<li>
50 aves.
</li>
<li>
25 aves.
</li>
</ol>
<br/>
<h4>Solution</h4>
<p>Para determinar la cantidad de aves que había al iniciar un monitoreo ecológico, necesitamos evaluar la función de población <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mi>k</mi><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>3</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mi>k</mi></mrow></msup></mrow><annotation encoding="application/x-tex">r(k) = 50(3)^{0.4k}</annotation></semantics></math> en el momento inicial, es decir, cuando el tiempo <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>k</mi><mo>=</mo><mn>0</mn></mrow><annotation encoding="application/x-tex">k = 0</annotation></semantics></math>.</p>
<p>La función dada es:
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mi>k</mi><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>3</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mi>k</mi></mrow></msup></mrow><annotation encoding="application/x-tex">r(k) = 50(3)^{0.4k}</annotation></semantics></math></p>
<p>Sustituimos <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>k</mi><mo>=</mo><mn>0</mn></mrow><annotation encoding="application/x-tex">k = 0</annotation></semantics></math> en la función:
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>0</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>3</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mo>⋅</mo><mn>0</mn></mrow></msup></mrow><annotation encoding="application/x-tex">r(0) = 50(3)^{0.4 \cdot 0}</annotation></semantics></math>
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>0</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>3</mn><mo stretchy="true" form="postfix">)</mo></mrow><mn>0</mn></msup></mrow><annotation encoding="application/x-tex">r(0) = 50(3)^0</annotation></semantics></math></p>
<p>Cualquier número (distinto de cero) elevado a la potencia 0 es igual a 1. Por lo tanto, <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>3</mn><mo stretchy="true" form="postfix">)</mo></mrow><mn>0</mn></msup><mo>=</mo><mn>1</mn></mrow><annotation encoding="application/x-tex">(3)^0 = 1</annotation></semantics></math>.
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>0</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn><mo>⋅</mo><mn>1</mn></mrow><annotation encoding="application/x-tex">r(0) = 50 \cdot 1</annotation></semantics></math>
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>r</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>0</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>50</mn></mrow><annotation encoding="application/x-tex">r(0) = 50</annotation></semantics></math></p>
<p>Así, la cantidad de aves que había al iniciar un monitoreo ecológico es 50.</p>
<br/>
<ol type="a">
<li>
Falso
</li>
<li>
Falso
</li>
<li>
Verdadero
</li>
<li>
Falso
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
