<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 1</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 1</h2>

<ol>
<li>
<h4>Question</h4>
<p>una nueva especie de virus evoluciona su cantidad según la función <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>P</mi><mrow><mo stretchy="true" form="prefix">(</mo><mi>k</mi><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>25</mn><mo>⋅</mo><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>1.5</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mo>⋅</mo><mi>k</mi></mrow></msup></mrow><annotation encoding="application/x-tex">P(k) = 25 \cdot (1.5)^{0.4 \cdot k}</annotation></semantics></math>, donde <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mi>k</mi><annotation encoding="application/x-tex">k</annotation></semantics></math> se mide en meses y <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mi>P</mi><annotation encoding="application/x-tex">P</annotation></semantics></math> es la cantidad de virus.</p>
<p>De acuerdo con la información anterior, la cantidad de virus que había a los(as) 1 meses de iniciado(a) un análisis poblacional, es:</p>
<br/>
<ol type="a">
<li>
24 virus.
</li>
<li>
19 virus.
</li>
<li>
29 virus.
</li>
<li>
28 virus.
</li>
</ol>
<br/>
<h4>Solution</h4>
<p>Para determinar la cantidad de virus que había al(los) 1 meses de iniciado un análisis poblacional, necesitamos evaluar la función de población <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>P</mi><mrow><mo stretchy="true" form="prefix">(</mo><mi>k</mi><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>25</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>1.5</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mi>k</mi></mrow></msup></mrow><annotation encoding="application/x-tex">P(k) = 25(1.5)^{0.4k}</annotation></semantics></math> en el momento <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>k</mi><mo>=</mo><mn>1</mn></mrow><annotation encoding="application/x-tex">k=1</annotation></semantics></math>.</p>
<p>La función dada es:
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>P</mi><mrow><mo stretchy="true" form="prefix">(</mo><mi>k</mi><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>25</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>1.5</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mi>k</mi></mrow></msup></mrow><annotation encoding="application/x-tex">P(k) = 25(1.5)^{0.4k}</annotation></semantics></math></p>
<p>Sustituimos <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>k</mi><mo>=</mo><mn>1</mn></mrow><annotation encoding="application/x-tex">k = 1</annotation></semantics></math> en la función:
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>P</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>1</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>=</mo><mn>25</mn><msup><mrow><mo stretchy="true" form="prefix">(</mo><mn>1.5</mn><mo stretchy="true" form="postfix">)</mo></mrow><mrow><mn>0.4</mn><mo>⋅</mo><mn>1</mn></mrow></msup></mrow><annotation encoding="application/x-tex">P(1) = 25(1.5)^{0.4 \cdot 1}</annotation></semantics></math>.</p>
<p>Primero calculamos el exponente: 0.4 <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mi>⋅</mi><annotation encoding="application/x-tex">\cdot</annotation></semantics></math> 1 = 0.4.</p>
<p>Luego, calculamos la base elevada al exponente: (1.5)<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><msup><mi></mi><mn>0.4</mn></msup><annotation encoding="application/x-tex">^{0.4}</annotation></semantics></math> <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mo>≈</mo><annotation encoding="application/x-tex">\approx</annotation></semantics></math> 1.18.</p>
<p>Finalmente, multiplicamos por el coeficiente inicial: 25 <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mi>⋅</mi><annotation encoding="application/x-tex">\cdot</annotation></semantics></math> 1.18 <math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mo>≈</mo><annotation encoding="application/x-tex">\approx</annotation></semantics></math> 29.4.</p>
<p><math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>P</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>1</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>≈</mo><mn>29.4</mn></mrow><annotation encoding="application/x-tex">P(1) \approx 29.4</annotation></semantics></math></p>
<p>Redondeando al número entero más cercano, obtenemos:
<math display="inline" xmlns="http://www.w3.org/1998/Math/MathML"><semantics><mrow><mi>P</mi><mrow><mo stretchy="true" form="prefix">(</mo><mn>1</mn><mo stretchy="true" form="postfix">)</mo></mrow><mo>≈</mo><mn>29</mn></mrow><annotation encoding="application/x-tex">P(1) \approx 29</annotation></semantics></math></p>
<p>Así, la cantidad de virus que había al(los) 1 meses de iniciado el un análisis poblacional es aproximadamente 29.</p>
<br/>
<ol type="a">
<li>
Falso
</li>
<li>
Falso
</li>
<li>
Verdadero
</li>
<li>
Falso
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
