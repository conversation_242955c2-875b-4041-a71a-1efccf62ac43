```{r data generation, echo = FALSE, results = "hide"}
## DATA GENERATION
# Configuracion inicial
Sys.setlocale("LC_ALL", "C")
options(OutDec = ".")
options(scipen = 999)

# Generar numero de calificaciones (entre 4 y 6)
num_calificaciones <- sample(4:6, 1)

# Generar calificaciones realistas (entre 2.5 y 5.0)
calificaciones <- round(runif(num_calificaciones, 2.5, 5.0), 1)

# Calcular la media aritmetica correcta
media_correcta <- round(mean(calificaciones), 1)
suma_total <- sum(calificaciones)

# Contextos colombianos variados
contextos <- list(
  list(
    estudiante = sample(c("Ana Sofia", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Isabella", "<PERSON> Alejandro"), 1),
    materia = "Matematicas",
    periodo = "primer bimestre",
    actividad = "examenes"
  ),
  list(
    estudiante = sample(c("<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ntina", "<PERSON>", "<PERSON>", "<PERSON>"), 1),
    materia = "Ciencias Naturales",
    periodo = "segundo periodo",
    actividad = "laboratorios"
  ),
  list(
    estudiante = sample(c("Alejandra", "Daniel", "Natalia", "Felipe", "Mariana", "Alejandro"), 1),
    materia = "Lengua Castellana",
    periodo = "tercer periodo",
    actividad = "ensayos"
  )
)

contexto_seleccionado <- sample(contextos, 1)[[1]]

## QUESTION/ANSWER GENERATION
# Generar distractores
distractores <- c()

# Distractor 1: mediana
mediana_val <- round(median(calificaciones), 1)
if(mediana_val != media_correcta) {
  distractores <- c(distractores, mediana_val)
}

# Distractor 2: valor maximo
max_val <- max(calificaciones)
if(max_val != media_correcta) {
  distractores <- c(distractores, max_val)
}

# Distractor 3: promedio de extremos
extremos_val <- round((min(calificaciones) + max(calificaciones)) / 2, 1)
if(extremos_val != media_correcta) {
  distractores <- c(distractores, extremos_val)
}

# Completar con valores aleatorios si es necesario
while(length(distractores) < 4) {
  nuevo_val <- round(runif(1, 2.0, 5.0), 1)
  if(!(nuevo_val %in% c(distractores, media_correcta))) {
    distractores <- c(distractores, nuevo_val)
  }
}

## TRANSFORM TO SINGLE CHOICE
questions <- c(media_correcta, distractores[1:4])
solutions <- c(TRUE, rep(FALSE, 4))

o <- sample(1:5)
questions <- questions[o]
solutions <- solutions[o]
```

Question
========
En el `r contexto_seleccionado$periodo` de `r contexto_seleccionado$materia`, `r contexto_seleccionado$estudiante` obtuvo las siguientes calificaciones en `r num_calificaciones` `r contexto_seleccionado$actividad`:

`r paste(calificaciones, collapse = ", ")`

¿Cual es la media aritmetica de estas calificaciones?

```{r questionlist, echo = FALSE, results = "asis"}
answerlist(questions, markup = "markdown")
```

Solution
========
Para calcular la media aritmetica de las calificaciones de `r contexto_seleccionado$estudiante`, debemos seguir estos pasos:

**Paso 1:** Sumar todas las calificaciones
`r paste(calificaciones, collapse = " + ")` = `r suma_total`

**Paso 2:** Dividir la suma por el numero total de calificaciones
`r suma_total` / `r num_calificaciones` = `r media_correcta`

**Respuesta:** La media aritmetica es `r media_correcta`

**Explicacion:** La media aritmetica (o promedio) se calcula sumando todos los valores y dividiendo el resultado por la cantidad total de datos. En este caso, `r contexto_seleccionado$estudiante` tuvo `r num_calificaciones` calificaciones que suman `r suma_total`, por lo que su promedio es `r media_correcta`.

```{r solutionlist, echo = FALSE, results = "asis"}
answerlist(ifelse(solutions, "Correcto", "Incorrecto"), markup = "markdown")
```

Meta-information
================
extype: schoice
exsolution: `r mchoice2string(solutions, single = TRUE)`
exname: Media Aritmetica Calificaciones
