<<echo=FALSE, results=hide>>=
## parameters
a <- sample(2:9, 1)
b <- sample(seq(2, 4, 0.1), 1)
c <- sample(seq(0.5, 0.8, 0.01), 1)
## solution
res <- exp(b * c) * (a * c^(a-1) + b * c^a)
@

\begin{question}
What is the derivative of $f(x) = x^{\Sexpr{a}} e^{\Sexpr{b}x}$, evaluated at $x = \Sexpr{c}$?
\end{question}

\begin{solution}
Using the product rule for $f(x) = g(x) \cdot h(x)$, where $g(x) := x^{\Sexpr{a}}$ and $h(x) := e^{\Sexpr{b}x}$, we obtain
\begin{eqnarray*}
f'(x) & = & [g(x) \cdot h(x)]' = g'(x) \cdot h(x) + g(x) \cdot h'(x) \\
      & = & \Sexpr{a} x^{\Sexpr{a} - 1} \cdot e^{\Sexpr{b}x} + x^{\Sexpr{a}} \cdot e^{\Sexpr{b}x} \cdot \Sexpr{b} \\
      & = & e^{\Sexpr{b}x} \cdot(\Sexpr{a} x^\Sexpr{a-1} + \Sexpr{b} x^{\Sexpr{a}}) \\
      & = & e^{\Sexpr{b}x} \cdot x^\Sexpr{a-1} \cdot (\Sexpr{a} + \Sexpr{b}x).
\end{eqnarray*}
Evaluated at $x = \Sexpr{c}$, the answer is
\[ e^{\Sexpr{b}\cdot \Sexpr{c}} \cdot \Sexpr{c}^\Sexpr{a-1} \cdot (\Sexpr{a} + \Sexpr{b}\cdot \Sexpr{c}) = \Sexpr{fmt(res, 6)}. \]
Thus, rounded to two digits we have $f'(\Sexpr{c}) = \Sexpr{fmt(res)}$.
\end{solution}

%% \extype{num}
%% \exsolution{\Sexpr{fmt(res)}}
%% \exname{derivative exp}
%% \extol{0.01}
