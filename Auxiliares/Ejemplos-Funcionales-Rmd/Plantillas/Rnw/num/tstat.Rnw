<<echo=FALSE, results=hide>>=
## DATA GENERATION
n <- sample(120:250, 1)
mu <- sample(c(125, 200, 250, 500, 1000), 1)
y <- rnorm(n, mean = mu * runif(1, min = 0.9, max = 1.1), 
           sd = mu * runif(1, min = 0.02, max = 0.06))

## QUESTION/ANSWER GENERATION
Mean <- round(mean(y), digits = 1)
Var <- round(var(y), digits = 2)
tstat <- round((Mean - mu)/sqrt(Var/n), digits = 3)
@

\begin{question}
A machine fills milk into $\Sexpr{mu}$ml packages. It is suspected that the 
machine is not working correctly and that the amount of milk filled differs 
from the setpoint $\mu_0 = \Sexpr{mu}$. A sample of $\Sexpr{n}$ packages 
filled by the machine are collected. The sample mean $\bar{y}$ is equal to 
$\Sexpr{Mean}$ and the sample variance $s^2_{n-1}$ is equal to 
$\Sexpr{Var}$.

Test the hypothesis that the amount filled corresponds on average to the 
setpoint. What is the absolute value of the $t$~test statistic?
\end{question}

\begin{solution}
The $t$~test statistic is calculated by:
\begin{eqnarray*}
  t & = & \frac{\bar y - \mu_0}{\sqrt{\frac{s^2_{n-1}}{n}}}
  = \frac{\Sexpr{Mean} - \Sexpr{mu}}{\sqrt{\frac{\Sexpr{Var}}{\Sexpr{n}}}}
  = \Sexpr{tstat}.
\end{eqnarray*}
The absolute value of the $t$~test statistic is thus equal to
$\Sexpr{fmt(abs(tstat), 3)}$.
\end{solution}

%% META-INFORMATION
%% \extype{num}
%% \exsolution{\Sexpr{fmt(abs(tstat), 3)}}
%% \exname{t statistic}
%% \extol{0.01}
