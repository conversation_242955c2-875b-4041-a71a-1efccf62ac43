\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
library(exams)
ok <- FALSE
while(!ok) {
## funciones auxiliares
SK <- function(x) diff(diff(fivenum(x)[2:4]))/diff(fivenum(x)[c(2, 4)])
trob <- function(a, b)
  (median(a) - median(b))/sqrt(var(a)/length(a) + var(b)/length(b))

## GENERACIÓN DE DATOS
## dgp para una muestra
dgp <- function(location = 0, scale = 1, skewed = FALSE, outlier = NULL,
  n = 10, amount = 0.1)
{
  ## intervalos básicos de los cuales se extraen cantidades iguales de observaciones
  qq <- if (skewed) c(0, 2, 2.2, 6, 10) else c(0, 3, 5, 7, 10)
  sim <- function(x) {
    rval <- NULL
    for(i in 1:(length(x)-1)) rval <- c(rval, runif(n, min = x[i], max = x[i+1]))
    rval <- jitter(rval, amount = amount)
    rval <- rval/4
    rval
  }
  ## extraer bajo restricciones sobre IQR y SK
  rval <- sim(qq)
  if (skewed) {
    while(IQR(rval) > 1.15 | IQR(rval) < 0.85 | abs(SK(rval)) < 0.7) rval <- sim(qq)
  } else {
    while(IQR(rval) > 1.15 | IQR(rval) < 0.85 |abs(SK(rval)) > 0.15) rval <- sim(qq)
  }

  ## valores brutos (location = 0, scale = 1)
  rval <- rval - ifelse(skewed, 0.55, 1.25)

  ## añadir valores atípicos
  rval <- c(rval, outlier)

  ## escalar y desplazar
  rval <- rval * scale + location

  return(rval)
}

## generar parámetros aleatorios para dgp()
scale <- sample(c(1, sample(c(1, 3), 1))) * runif(1, min = 0.5, max = 10) * sample(c(-1, 1), 1)
location <- sample(c(0, sample(c(0, 2), 1))) * scale + runif(1, min = -50, max = 50)
skewed <- if (runif(1) < 2/3) c(FALSE, FALSE) else sample(c(TRUE, sample(c(TRUE, FALSE), 1)))
if (runif(1) < 2/3) {
  outlier <- list(NULL, NULL)
} else {
  if (any(skewed)) {
    outlier <- if (skewed[1]) -sign(scale[1]) * runif(sample(1:2, 1), min = 3, max = 4) else NULL
    outlier <- c(list(outlier), if (skewed[2])
      list(-sign(scale[1]) * runif(sample((0:1 + !skewed[1]), 1), min = 3, max = 4)) else list(NULL))
  } else {
    outlier <- runif(sample(1:3, 1), min = 3, max = 4)
    outlier <- outlier * sample(c(-1, 1), length(outlier), replace = TRUE)
    id <- sample(1:length(outlier), sample(1:length(outlier), 1))
    outlier1 <- outlier[id]
    outlier2 <- outlier[-id]
    outlier <- list(if(length(outlier1) > 0) outlier1 else NULL,
      if (length(outlier2) > 0) outlier2 else NULL)
  }
}

## llamar a dgp bajo ciertas restricciones
A <- dgp(location = location[1], scale = scale[1], skewed = skewed[1], outlier = outlier[[1]],
  n = sample(8:12, 1))
B <- dgp(location = location[2], scale = scale[2], skewed = skewed[2], outlier = outlier[[2]],
  n = sample(8:12, 1))
while((length(unique(location)) < 2 & abs(trob(A, B)) > 0.4) | (abs(SK(A)) > 0.15 & abs(SK(A)) < 0.7) | (abs(SK(B)) > 0.15 & abs(SK(B)) < 0.7)) {
  A <- dgp(location = location[1], scale = scale[1], skewed = skewed[1], outlier = outlier[[1]],
    n = sample(8:12, 1))
  B <- dgp(location = location[2], scale = scale[2], skewed = skewed[2], outlier = outlier[[2]],
    n = sample(8:12, 1))
}
SK_A <- ifelse(abs(SK(A)) < 0.2, "simétrica", ifelse(SK(A) >= 0.2, "sesgada a la derecha", "sesgada a la izquierda"))
SK_B <- ifelse(abs(SK(B)) < 0.2, "simétrica", ifelse(SK(B) >= 0.2, "sesgada a la derecha", "sesgada a la izquierda"))

## GENERACIÓN DE PREGUNTAS/RESPUESTAS
questions <- character(5)
solutions <- logical(5)
explanations <- character(5)

questions[1] <- "La ubicación de ambas distribuciones es aproximadamente la misma."
solutions[1] <- abs(trob(A, B)) < 0.5
explanations[1] <- if (solutions[1]) "Ambas distribuciones tienen una ubicación similar." else
  paste("La distribución ", c("A", "B")[(median(A) < median(B)) + 1],
    " tiene en promedio valores más altos que la distribución ",
    c("A", "B")[(median(A) >= median(B)) + 1], ".", sep = "")

outlier <- length(c(boxplot.stats(A)$out, boxplot.stats(B)$out)) > 0 ## recalcular para que coincida con la visualización
questions[2] <- "Ambas distribuciones no contienen valores atípicos."
solutions[2] <- !outlier
explanations[2] <- if (solutions[2])
  "Ambas distribuciones no tienen observaciones que se desvíen más de 1.5 veces el rango intercuartílico desde la caja." else "Hay observaciones que se desvían más de 1.5 veces el rango intercuartílico desde la caja."

questions[3] <- "La dispersión en la muestra A es claramente mayor que en B."
solutions[3] <- IQR(A)/IQR(B) > 1.5
explanations[3] <- paste("El rango intercuartílico en la muestra A es",
  ifelse(solutions[3], "", "\\\\textit{no}"), "claramente mayor que en B.")

questions[4] <- "La asimetría de ambas muestras es similar."
solutions[4] <- SK_A == SK_B
explanations[4] <- if (solutions[4]) paste("La asimetría de ambas distribuciones es similar, ambas son",
    ifelse(abs(SK(A)) < 0.2, "aproximadamente simétricas.", ifelse(SK(A) >= 0.2, "sesgadas a la derecha.", "sesgadas a la izquierda."))) else
    paste("La asimetría de ambas distribuciones es diferente. La muestra A es",
    ifelse(abs(SK(A)) < 0.2, "aproximadamente simétrica.", ifelse(SK(A) >= 0.2, "sesgada a la derecha.", "sesgada a la izquierda.")),
    "La muestra B es", ifelse(abs(SK(B)) < 0.2, "aproximadamente simétrica.", ifelse(SK(B) >= 0.2, "sesgada a la derecha.", "sesgada a la izquierda.")))

i <- sample(1:2, 1)
j <- sample(1:3, 1)
questions[5] <- paste("La distribución ", c("A", "B")[i], " es ",
                      c("aproximadamente simétrica", "sesgada a la derecha", "sesgada a la izquierda")[j], ".", sep = "")
SK_i <- SK(list(A, B)[[i]])
solutions[5] <- switch(j, abs(SK_i) < 0.2,
                       SK_i >= 0.2,
                       SK_i <= -0.2)
explanations[5] <- paste("La distribución ", c("A", "B")[i], " es ",
                         ifelse(abs(SK_i) < 0.2, "aproximadamente simétrica.",
                                ifelse(SK_i >= 0.2, "sesgada a la derecha.", "sesgada a la izquierda.")))

ok <- any(solutions) && any(!solutions)
}
@

\begin{question}
En la siguiente figura se representan las distribuciones de una variable
dada por dos muestras (A y B) mediante diagramas de caja paralelos.
¿Cuáles de las siguientes afirmaciones son correctas? \emph{(Comentario: Las
afirmaciones son correctas o claramente incorrectas.)}

\setkeys{Gin}{width=0.7\textwidth}
<<fig=TRUE, height=4, width=5, echo=FALSE, eps=FALSE, results=hide>>=
par(mar = c(2.5, 2, 1, 0.5))
dat <- data.frame(y = c(A, B), x = factor(rep(c("A", "B"), c(length(A), length(B)))))
boxplot(y ~ x, data = dat, xlab = "", ylab = "")
@

<<echo=FALSE, results=tex>>=
answerlist(questions)
@

\end{question}

\begin{solution}
<<echo=FALSE, results=tex>>=
answerlist(
  ifelse(solutions, "Verdadero", "Falso"),
  explanations)
@

\end{solution}

%% META-INFORMATION
%% \extype{mchoice}
%% \exsolution{\Sexpr{mchoice2string(solutions)}}
%% \exname{Diagramas de caja paralelos}
\end{document}
