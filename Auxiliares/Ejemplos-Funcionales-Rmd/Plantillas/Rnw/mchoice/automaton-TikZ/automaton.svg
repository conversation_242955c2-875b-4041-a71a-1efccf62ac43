<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="156pt" height="128pt" viewBox="0 0 156 128">
<defs>
<g>
<g id="glyph-0-0">
<path d="M 5.671875 -2.625 L 6.5625 0 L 7.8125 0 L 4.75 -8.71875 L 3.3125 -8.71875 L 0.203125 0 L 1.390625 0 L 2.3125 -2.625 Z M 5.359375 -3.546875 L 2.578125 -3.546875 L 4.015625 -7.515625 Z M 5.359375 -3.546875 "/>
</g>
<g id="glyph-0-1">
<path d="M 5.234375 -4.515625 C 5.21875 -5.75 4.40625 -6.4375 2.96875 -6.4375 C 1.5 -6.4375 0.5625 -5.6875 0.5625 -4.53125 C 0.5625 -3.546875 1.0625 -3.078125 2.546875 -2.71875 L 3.484375 -2.5 C 4.171875 -2.328125 4.453125 -2.078125 4.453125 -1.625 C 4.453125 -1.046875 3.859375 -0.640625 2.984375 -0.640625 C 2.453125 -0.640625 2 -0.796875 1.75 -1.0625 C 1.59375 -1.25 1.515625 -1.421875 1.453125 -1.859375 L 0.40625 -1.859375 C 0.453125 -0.421875 1.265625 0.28125 2.90625 0.28125 C 4.484375 0.28125 5.484375 -0.5 5.484375 -1.703125 C 5.484375 -2.640625 4.953125 -3.15625 3.71875 -3.453125 L 2.765625 -3.6875 C 1.953125 -3.875 1.609375 -4.140625 1.609375 -4.578125 C 1.609375 -5.15625 2.109375 -5.515625 2.921875 -5.515625 C 3.734375 -5.515625 4.15625 -5.171875 4.1875 -4.515625 Z M 5.234375 -4.515625 "/>
</g>
<g id="glyph-0-2">
<path d="M 3.03125 -6.265625 L 2.015625 -6.265625 L 2.015625 -7.984375 L 1.015625 -7.984375 L 1.015625 -6.265625 L 0.171875 -6.265625 L 0.171875 -5.453125 L 1.015625 -5.453125 L 1.015625 -0.71875 C 1.015625 -0.078125 1.453125 0.28125 2.21875 0.28125 C 2.46875 0.28125 2.703125 0.25 3.03125 0.1875 L 3.03125 -0.640625 C 2.90625 -0.609375 2.75 -0.59375 2.5625 -0.59375 C 2.125 -0.59375 2.015625 -0.71875 2.015625 -1.15625 L 2.015625 -5.453125 L 3.03125 -5.453125 Z M 3.03125 -6.265625 "/>
</g>
<g id="glyph-0-3">
<path d="M 6.390625 -0.578125 C 6.28125 -0.5625 6.234375 -0.5625 6.1875 -0.5625 C 5.828125 -0.5625 5.640625 -0.734375 5.640625 -1.046875 L 5.640625 -4.734375 C 5.640625 -5.84375 4.828125 -6.4375 3.28125 -6.4375 C 2.375 -6.4375 1.625 -6.1875 1.203125 -5.71875 C 0.921875 -5.390625 0.796875 -5.03125 0.78125 -4.40625 L 1.78125 -4.40625 C 1.859375 -5.171875 2.3125 -5.515625 3.25 -5.515625 C 4.140625 -5.515625 4.65625 -5.1875 4.65625 -4.59375 L 4.65625 -4.328125 C 4.65625 -3.90625 4.40625 -3.734375 3.609375 -3.640625 C 2.203125 -3.453125 1.984375 -3.40625 1.609375 -3.25 C 0.875 -2.953125 0.5 -2.390625 0.5 -1.578125 C 0.5 -0.4375 1.296875 0.28125 2.5625 0.28125 C 3.34375 0.28125 3.984375 0 4.6875 -0.640625 C 4.75 -0.015625 5.0625 0.28125 5.71875 0.28125 C 5.921875 0.28125 6.078125 0.25 6.390625 0.171875 Z M 4.65625 -1.96875 C 4.65625 -1.640625 4.546875 -1.4375 4.25 -1.15625 C 3.84375 -0.78125 3.359375 -0.59375 2.765625 -0.59375 C 2 -0.59375 1.546875 -0.96875 1.546875 -1.609375 C 1.546875 -2.265625 1.984375 -2.59375 3.046875 -2.75 C 4.09375 -2.890625 4.3125 -2.9375 4.65625 -3.09375 Z M 4.65625 -1.96875 "/>
</g>
<g id="glyph-0-4">
<path d="M 0.828125 -6.265625 L 0.828125 0 L 1.828125 0 L 1.828125 -3.25 C 1.828125 -4.140625 2.0625 -4.734375 2.53125 -5.078125 C 2.84375 -5.3125 3.140625 -5.375 3.84375 -5.390625 L 3.84375 -6.40625 C 3.671875 -6.4375 3.578125 -6.4375 3.453125 -6.4375 C 2.8125 -6.4375 2.3125 -6.0625 1.75 -5.125 L 1.75 -6.265625 Z M 0.828125 -6.265625 "/>
</g>
<g id="glyph-0-5">
<path d="M 0.9375 0 L 4.875 0 C 5.703125 0 6.3125 -0.234375 6.78125 -0.734375 C 7.203125 -1.1875 7.453125 -1.8125 7.453125 -2.484375 C 7.453125 -3.53125 6.96875 -4.171875 5.859375 -4.609375 C 6.640625 -4.96875 7.0625 -5.625 7.0625 -6.5 C 7.0625 -7.140625 6.828125 -7.703125 6.375 -8.109375 C 5.921875 -8.515625 5.3125 -8.71875 4.484375 -8.71875 L 0.9375 -8.71875 Z M 2.0625 -4.953125 L 2.0625 -7.734375 L 4.203125 -7.734375 C 4.828125 -7.734375 5.171875 -7.65625 5.46875 -7.421875 C 5.78125 -7.1875 5.953125 -6.828125 5.953125 -6.34375 C 5.953125 -5.875 5.78125 -5.515625 5.46875 -5.265625 C 5.171875 -5.046875 4.828125 -4.953125 4.203125 -4.953125 Z M 2.0625 -0.984375 L 2.0625 -3.984375 L 4.765625 -3.984375 C 5.3125 -3.984375 5.671875 -3.84375 5.921875 -3.5625 C 6.1875 -3.28125 6.328125 -2.90625 6.328125 -2.46875 C 6.328125 -2.0625 6.1875 -1.671875 5.921875 -1.40625 C 5.671875 -1.109375 5.3125 -0.984375 4.765625 -0.984375 Z M 2.0625 -0.984375 "/>
</g>
<g id="glyph-0-6">
<path d="M 7.90625 -6.015625 C 7.5625 -7.921875 6.46875 -8.859375 4.546875 -8.859375 C 3.375 -8.859375 2.4375 -8.484375 1.796875 -7.765625 C 1 -6.90625 0.578125 -5.671875 0.578125 -4.25 C 0.578125 -2.828125 1.015625 -1.59375 1.84375 -0.734375 C 2.515625 -0.046875 3.375 0.28125 4.5 0.28125 C 6.640625 0.28125 7.828125 -0.875 8.09375 -3.171875 L 6.9375 -3.171875 C 6.84375 -2.578125 6.734375 -2.171875 6.546875 -1.828125 C 6.1875 -1.109375 5.453125 -0.703125 4.515625 -0.703125 C 2.78125 -0.703125 1.6875 -2.09375 1.6875 -4.265625 C 1.6875 -6.5 2.734375 -7.875 4.421875 -7.875 C 5.125 -7.875 5.78125 -7.65625 6.140625 -7.328125 C 6.46875 -7.03125 6.640625 -6.65625 6.78125 -6.015625 Z M 7.90625 -6.015625 "/>
</g>
<g id="glyph-0-7">
<path d="M 1.0625 0 L 4.421875 0 C 6.625 0 7.96875 -1.65625 7.96875 -4.359375 C 7.96875 -7.0625 6.640625 -8.71875 4.421875 -8.71875 L 1.0625 -8.71875 Z M 2.171875 -0.984375 L 2.171875 -7.734375 L 4.234375 -7.734375 C 5.953125 -7.734375 6.859375 -6.578125 6.859375 -4.34375 C 6.859375 -2.15625 5.953125 -0.984375 4.234375 -0.984375 Z M 2.171875 -0.984375 "/>
</g>
<g id="glyph-0-8">
<path d="M 3.09375 -6.03125 L 3.09375 0 L 4.140625 0 L 4.140625 -8.46875 L 3.453125 -8.46875 C 3.078125 -7.171875 2.84375 -7 1.21875 -6.796875 L 1.21875 -6.03125 Z M 3.09375 -6.03125 "/>
</g>
<g id="glyph-0-9">
<path d="M 3.28125 -8.46875 C 2.5 -8.46875 1.78125 -8.125 1.34375 -7.546875 C 0.78125 -6.796875 0.515625 -5.671875 0.515625 -4.09375 C 0.515625 -1.25 1.46875 0.28125 3.28125 0.28125 C 5.078125 0.28125 6.0625 -1.25 6.0625 -4.03125 C 6.0625 -5.671875 5.796875 -6.78125 5.234375 -7.546875 C 4.796875 -8.140625 4.09375 -8.46875 3.28125 -8.46875 Z M 3.28125 -7.546875 C 4.421875 -7.546875 4.984375 -6.390625 4.984375 -4.125 C 4.984375 -1.71875 4.4375 -0.59375 3.265625 -0.59375 C 2.15625 -0.59375 1.59375 -1.765625 1.59375 -4.09375 C 1.59375 -6.40625 2.15625 -7.546875 3.28125 -7.546875 Z M 3.28125 -7.546875 "/>
</g>
</g>
</defs>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 12.453594 -0.0016875 C 12.453594 6.877219 6.879375 12.455344 0.00046875 12.455344 C -6.878437 12.455344 -12.452656 6.877219 -12.452656 -0.0016875 C -12.452656 -6.876688 -6.878437 -12.454813 0.00046875 -12.454813 C 6.879375 -12.454813 12.453594 -6.876688 12.453594 -0.0016875 Z M 12.453594 -0.0016875 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="51.868" y="27.312"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -28.284687 -0.0016875 L -14.108906 -0.0016875 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.196969 1.592062 C -1.095406 0.994406 -0.00165625 0.099875 0.299125 -0.0016875 C -0.00165625 -0.0993438 -1.095406 -0.997781 -1.196969 -1.595438 " transform="matrix(1, 0, 0, -1, 41.74775, 23.053)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="0.321" y="27.04"/>
<use xlink:href="#glyph-0-2" x="6.2986" y="27.04"/>
<use xlink:href="#glyph-0-3" x="9.622146" y="27.04"/>
<use xlink:href="#glyph-0-4" x="16.269237" y="27.04"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="20.728526" y="27.04"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 12.453594 -81.997781 C 12.453594 -75.122781 6.879375 -69.544656 0.00046875 -69.544656 C -6.878437 -69.544656 -12.452656 -75.122781 -12.452656 -81.997781 C -12.452656 -88.876688 -6.878437 -94.450906 0.00046875 -94.450906 C 6.879375 -94.450906 12.453594 -88.876688 12.453594 -81.997781 Z M 12.453594 -81.997781 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-5" x="51.868" y="109.31"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 94.453594 -0.0016875 C 94.453594 6.877219 88.875469 12.455344 82.000469 12.455344 C 75.121563 12.455344 69.547344 6.877219 69.547344 -0.0016875 C 69.547344 -6.876688 75.121563 -12.454813 82.000469 -12.454813 C 88.875469 -12.454813 94.453594 -6.876688 94.453594 -0.0016875 Z M 94.453594 -0.0016875 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-6" x="133.537" y="27.327"/>
</g>
<path fill="none" stroke-width="1.39478" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 94.453594 -82.298563 C 94.453594 -75.419656 88.875469 -69.845438 82.000469 -69.845438 C 75.121563 -69.845438 69.547344 -75.419656 69.547344 -82.298563 C 69.547344 -89.177469 75.121563 -94.751688 82.000469 -94.751688 C 88.875469 -94.751688 94.453594 -89.177469 94.453594 -82.298563 Z M 94.453594 -82.298563 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.59776" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(100%, 100%, 100%)" stroke-opacity="1" stroke-miterlimit="10" d="M 94.453594 -82.298563 C 94.453594 -75.419656 88.875469 -69.845438 82.000469 -69.845438 C 75.121563 -69.845438 69.547344 -75.419656 69.547344 -82.298563 C 69.547344 -89.177469 75.121563 -94.751688 82.000469 -94.751688 C 88.875469 -94.751688 94.453594 -89.177469 94.453594 -82.298563 Z M 94.453594 -82.298563 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-7" x="133.537" y="109.609"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 4.328594 -11.888406 C 12.094219 -33.224344 12.094219 -48.775125 4.824688 -68.743875 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.194118 1.593869 C -1.097495 0.997173 -0.00157126 0.0990658 0.300157 0.001034 C 0.000155044 -0.0998419 -1.094915 -0.997248 -1.193529 -1.594335 " transform="matrix(-0.34201, 0.93968, 0.93968, 0.34201, 60.67981, 91.79572)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-8" x="69.526" y="68.311"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 11.891094 4.326437 C 33.223125 12.092062 48.773906 12.092062 68.742656 4.826437 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.195862 1.595097 C -1.094232 0.996066 0.000355673 0.101629 0.299749 -0.00140908 C -0.00158876 -0.0986143 -1.096659 -0.996021 -1.195273 -1.593108 " transform="matrix(0.93968, 0.34201, 0.34201, -0.93968, 124.59772, 18.22819)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-9" x="93.53" y="9.137"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -4.327656 -70.111063 C -12.093281 -48.775125 -12.093281 -33.224344 -4.82375 -13.255594 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.194191 1.592845 C -1.097568 0.996148 0.00202662 0.0993776 0.300084 0.00000980686 C 0.000082185 -0.100866 -1.096324 -0.994602 -1.193602 -1.59536 " transform="matrix(0.34201, -0.93968, -0.93968, -0.34201, 51.03019, 36.30933)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-8" x="35.537" y="68.311"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 11.906719 -77.716531 C 33.234844 -69.997781 48.543438 -70.025125 68.480938 -77.325906 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.19751 1.593828 C -1.094755 0.994984 -0.00080878 0.0975855 0.297437 -0.00122358 C -0.0000521836 -0.0976518 -1.097113 -0.998461 -1.194607 -1.595736 " transform="matrix(0.93903, 0.34377, 0.34377, -0.93903, 124.3344, 100.37707)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-9" x="93.417" y="91.238"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 86.324688 -11.888406 C 94.160625 -33.298563 94.187969 -48.689188 86.922344 -68.7595 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.196064 1.592897 C -1.0968 0.997357 0.00125401 0.0986755 0.299142 -0.00120893 C -0.00103689 -0.101567 -1.093997 -0.995757 -1.197318 -1.594009 " transform="matrix(-0.34038, 0.94026, 0.94026, 0.34038, 142.77874, 91.81289)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-8" x="151.599" y="68.31"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 70.109844 -4.325906 C 48.773906 -12.091531 33.223125 -12.091531 13.254375 -4.825906 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.197454 1.593952 C -1.095824 0.994921 -0.00123633 0.100485 0.296821 0.00111687 C 0.000489973 -0.0984231 -1.09458 -0.99583 -1.196865 -1.594252 " transform="matrix(-0.93968, -0.34201, -0.34201, 0.93968, 69.11133, 27.87781)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-9" x="93.53" y="45.242"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 77.570781 -70.126688 C 69.809063 -48.689188 69.836406 -33.298563 77.172344 -13.255594 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.194349 1.592397 C -1.096692 0.995858 0.000804506 0.0996334 0.299039 0.000779929 C -0.000793334 -0.100617 -1.09567 -0.996265 -1.19692 -1.594875 " transform="matrix(0.34363, -0.93907, -0.93907, -0.34363, 133.02688, 36.30843)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-8" x="117.459" y="68.31"/>
</g>
<path fill="none" stroke-width="0.3985" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 69.812969 -86.681375 C 48.4575 -94.325906 33.145 -94.243875 13.23875 -86.872781 " transform="matrix(1, 0, 0, -1, 55.855, 23.053)"/>
<path fill="none" stroke-width="0.31879" stroke-linecap="round" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -1.194932 1.592835 C -1.095089 0.996647 -0.000272304 0.0980569 0.298327 0.000294344 C 0.00117077 -0.0971819 -1.095457 -0.99454 -1.194517 -1.593526 " transform="matrix(-0.93779, -0.34706, -0.34706, 0.93779, 69.09237, 109.92748)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-9" x="93.343" y="127.434"/>
</g>
</svg>
