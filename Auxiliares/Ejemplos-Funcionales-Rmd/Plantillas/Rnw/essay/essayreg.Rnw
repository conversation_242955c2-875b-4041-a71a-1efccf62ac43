<<echo=FALSE, results=hide>>=
## regression parameters
n <- sample(40:90, 1)
b <- sample(c(-1, 1), 1) * runif(1, 1, 2) * sample(c(0.1, 0.5, 1), 1)
s <- sample(c(0.5, 1, 2), 1)

## data and regression
d <- data.frame(
  x = rnorm(n),
  err = rnorm(n, sd = s)
)
d$y <- 0 + b * d$x + d$err

## different types
type <- sample(c("linear", "semi-logarithmic", "log-log"), 1)
if(type == "linear") {
  m <- lm(y ~ x, data = d)
  xunit <- "unit" 
  yunit <- "units"
  eff <- round(coef(m)[2], digits = 2)
} else if(type == "semi-logarithmic") {
  d$y <- exp(d$y)
  m <- lm(log(y) ~ x, data = d)
  xunit <- "unit" 
  yunit <- "percent"
  eff <- round(100 * exp(coef(m)[2]) - 100, digits = 2)
} else if(type == "log-log") {
  d$y <- exp(d$y)
  d$x <- exp(d$x)
  m <- lm(log(y) ~ log(x), data = d)
  xunit <- "percent" 
  yunit <- "percent"
  eff <- round(100 * exp(0.01 * coef(m)[2]) - 100, digits = 2)
}

## summaries
direct <- if(coef(m)[2] > 0) "increases" else "decreases"
if(summary(m)$coefficients[2, 4] < 0.05) {
  sign1 <- "Also"
  sign2 <- ""
} else {
  sign1 <- "However"
  sign2 <- "\\\\emph{not}"
}
@

\begin{question}
Consider the following regression results:

<<echo=FALSE>>=
summary(m)
@

Describe how the response \texttt{y} depends on the regressor \texttt{x}.
\end{question}

\begin{solution}
The presented results describe a \Sexpr{type} regression.

The mean of the response \texttt{y} \Sexpr{direct} with increasing \texttt{x}.

If \texttt{x} increases by $1$ \Sexpr{xunit} then a change of \texttt{y} by about $\Sexpr{eff}$ \Sexpr{yunit} can be expected.

\Sexpr{sign1}, the effect of \texttt{x} is \Sexpr{sign2} significant at the $5$ percent level.
\end{solution}

%% \extype{string}
%% \exsolution{nil}
%% \exname{regression essay}
%% \exstringtype{essay|file}
%% \exextra[essay,logical]{TRUE}
%% \exextra[essay_format,character]{editor}
%% \exextra[essay_required,logical]{FALSE}
%% \exextra[essay_fieldlines,numeric]{5}
%% \exextra[essay_attachments,numeric]{1}
%% \exextra[essay_attachmentsrequired,logical]{TRUE}
%% \exmaxchars{1000, 10, 50}

