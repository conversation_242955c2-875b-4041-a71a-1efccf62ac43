\documentclass[a4paper]{article}

\usepackage{amsmath,amssymb,amsfonts}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
# Cargar el paquete que contiene num_to_schoice
library(exams)
# O si es una función personalizada, source el archivo que la contiene
# source("ruta/al/archivo/con/funciones.R")

sc <- NULL
while(is.null(sc)) {
## parámetros
a <- sample(2:9, 1)
b <- sample(seq(2, 4, 0.1), 1)
c <- sample(seq(0.6, 0.9, 0.01), 1)
## solución
res <- exp(b * c) * (a * c^(a-1) + b * c^a)
## opciones de selección múltiple
err <- c(a * c^(a-1) * exp(b * c), a * c^(a-1) * exp(b * c) + c^a * exp(b * c))
# Añadir más opciones incorrectas para evitar el warning
err <- c(err, res * 0.7, res * 1.3)  # Añadir opciones más alejadas
rg <- if(res < 4) c(0.5, 5.5) else res * c(0.5, 1.5)
# Aumentar el valor de delta para permitir opciones más cercanas
sc <- num_to_schoice(res, wrong = err, range = rg, delta = 0.05)
}
@

\begin{question}
¿Cuál es la derivada de $f(x) = x^{\Sexpr{a}} e^{\Sexpr{b}x}$, evaluada en $x = \Sexpr{c}$?

<<echo=FALSE, results=tex>>=
answerlist(sc$questions)
@
\end{question}

\begin{solution}
Usando la regla del producto para $f(x) = g(x) \cdot h(x)$, donde $g(x) := x^{\Sexpr{a}}$ y $h(x) := e^{\Sexpr{b}x}$, obtenemos
\begin{eqnarray*}
f'(x) & = & [g(x) \cdot h(x)]' = g'(x) \cdot h(x) + g(x) \cdot h'(x) \\
      & = & \Sexpr{a} x^{\Sexpr{a} - 1} \cdot e^{\Sexpr{b}x} + x^{\Sexpr{a}} \cdot e^{\Sexpr{b}x} \cdot \Sexpr{b} \\
      & = & e^{\Sexpr{b}x} \cdot(\Sexpr{a} x^\Sexpr{a-1} + \Sexpr{b} x^{\Sexpr{a}}) \\
      & = & e^{\Sexpr{b}x} \cdot x^\Sexpr{a-1} \cdot (\Sexpr{a} + \Sexpr{b}x).
\end{eqnarray*}
Evaluada en $x = \Sexpr{c}$, la respuesta es
\[ e^{\Sexpr{b}\cdot \Sexpr{c}} \cdot \Sexpr{c}^\Sexpr{a-1} \cdot (\Sexpr{a} + \Sexpr{b}\cdot \Sexpr{c}) = \Sexpr{fmt(res, 6)}. \]
Por lo tanto, redondeando a dos dígitos tenemos $f'(\Sexpr{c}) = \Sexpr{fmt(res)}$.

<<echo=FALSE, results=tex>>=
answerlist(ifelse(sc$solutions, "Verdadero", "Falso"))
@
\end{solution}

%% \extype{schoice}
%% \exsolution{\Sexpr{mchoice2string(sc$solutions)}}
%% \exname{derivada exp}

\end{document}
