---
output:
  html_document:
    df_print: paged
    mathjax: true
  pdf_document: 
    latex_engine: xelatex
    keep_tex: true
  word_document: default
header-includes:
- \usepackage[spanish]{babel}
- \usepackage{amsmath}
- \usepackage{fontspec}
- \usepackage{unicode-math}
- \usepackage{graphicx}
- \usepackage{adjustbox}
- \usepackage{tikz}
- \usepackage{pgfplots}
- \usetikzlibrary{3d,babel}
---

```{r setup, include=FALSE}
# Librerías esenciales
library(exams)
library(ggplot2)
library(knitr)
library(reticulate)
library(testthat)
library(data.table)
library(readxl)
library(datasets)

# Configurar Python si es necesario
use_python(Sys.which("python"), required = TRUE)

# Configuración global
typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE, 
  message = FALSE,
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  echo = FALSE,
  results = "hide",
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Punto decimal para R interno (coma en presentación)

# Función de redondeo matemático correcto (hacia arriba para .5)
redondear_matematico <- function(x, digits = 0) {
  # CORRECCIÓN IMPORTANTE: R usa "redondeo bancario" donde .5 se redondea al número par más cercano
  # Ejemplo problemático: round(36.5, 0) = 36 (incorrecto matemáticamente)
  # Esta función implementa redondeo matemático estándar: .5 siempre hacia arriba
  # Ejemplos corregidos: 36.5 -> 37, 38.5 -> 39, 40.5 -> 41
  factor <- 10^digits
  return(floor(x * factor + 0.5) / factor)
}

# Función de formato estándar para números (sin separador de miles, punto decimal)
formato_estandar <- function(x, decimales = 0) {
  # FORMATO ESTÁNDAR: Sin separador de miles, punto como separador decimal
  # Ejemplos monetarios: 16850 (enteros), 16850.32 (con decimales)
  # Ejemplos numéricos: 36.5000 (con decimales)
  if (decimales == 0) {
    return(as.character(as.integer(x)))
  } else {
    # Para decimales, usar punto como separador (formato estándar R)
    resultado <- sprintf(paste0("%.", decimales, "f"), x)
    return(resultado)
  }
}

# Establecer semilla aleatoria para reproducibilidad
#set.seed(sample(1:10000, 1))

# Aleatorización de contexto y personajes (un nombre masculino y uno femenino)
nombres_masculinos <- c("Pedro", "Carlos", "Miguel", "Antonio", "Diego", "Sebastián", "Andrés", "Luis", "José", "Francisco")
nombres_femeninos <- c("María", "Ana", "Carmen", "Laura", "Sofía", "Valentina", "Isabella", "Camila", "Daniela", "Alejandra")

# Seleccionar aleatoriamente un nombre masculino y uno femenino
nombre_masculino <- sample(nombres_masculinos, 1)
nombre_femenino <- sample(nombres_femeninos, 1)

# Aleatorizar el orden (quién se menciona primero)
if(sample(c(TRUE, FALSE), 1)) {
  nombre1 <- nombre_masculino
  nombre2 <- nombre_femenino
} else {
  nombre1 <- nombre_femenino
  nombre2 <- nombre_masculino
}

# Aleatorización de contexto habitacional
tipos_vivienda <- c("apartamento", "hogar", "aparta-estudio")
tipo_vivienda <- sample(tipos_vivienda, 1)

# Aleatorizar meses involucrados en el problema
meses_disponibles <- c("Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
                      "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre")

# Seleccionar 7 meses consecutivos aleatoriamente
inicio_mes <- sample(1:6, 1)  # Asegurar que quepan 7 meses
meses_seleccionados <- meses_disponibles[inicio_mes:(inicio_mes + 6)]

# Asignar roles: mes de la pregunta (posición aleatoria) y mes de la factura (último mes)
posicion_pregunta <- sample(1:6, 1)  # No puede ser el último mes (reservado para factura)
mes_pregunta <- meses_seleccionados[posicion_pregunta]
mes_factura <- meses_seleccionados[7]  # Último mes para la factura

# Crear vector de meses y consumos (mantenemos la gráfica de consumos)
meses <- meses_seleccionados

# Generar consumos base realistas para la gráfica
consumos_base <- numeric(7)
for(i in 1:7) {
  consumos_base[i] <- sample(10:18, 1)  # Consumos realistas
}

consumos <- consumos_base
names(consumos) <- meses

# Generar datos económicos para el cálculo del costo promedio diario
# Generar cobro total del mes de la pregunta (entre $15000 y $25000)
cobro_total <- sample(15000:25000, 1)

# Generar valor por servicio fijo (entre $3000 y $8000)
valor_fijo <- sample(3000:8000, 1)

# Calcular costo promedio diario
costo_consumo_variable <- cobro_total - valor_fijo
costo_promedio_diario <- redondear_matematico(costo_consumo_variable / 30, 0)

# Calcular respuestas para formato cloze secuencial
respuesta_1 <- cobro_total                                    # Paso 1: Cobro total del mes
respuesta_2 <- valor_fijo                                     # Paso 2: Valor por servicio fijo
respuesta_3 <- costo_consumo_variable                        # Paso 3: Costo variable (cobro - fijo)
respuesta_4 <- 30                                            # Paso 4: Días de facturación
respuesta_5 <- redondear_matematico(costo_consumo_variable / 30, 4)         # Paso 5: División con 4 decimales
respuesta_6 <- costo_promedio_diario                         # Paso 6: Resultado final redondeado

# Generar distractores para la pregunta schoice final
distractores <- c()

# Distractor 1: No restar el valor fijo
distractor_1 <- redondear_matematico(cobro_total / 30, 0)
if(distractor_1 != respuesta_6 && distractor_1 > 0) {
  distractores <- c(distractores, distractor_1)
}

# Distractor 2: Dividir entre 31 días en lugar de 30
distractor_2 <- redondear_matematico(costo_consumo_variable / 31, 0)
if(distractor_2 != respuesta_6 && distractor_2 > 0 && !distractor_2 %in% distractores) {
  distractores <- c(distractores, distractor_2)
}

# Distractor 3: Usar solo el valor fijo dividido entre 30
distractor_3 <- redondear_matematico(valor_fijo / 30, 0)
if(distractor_3 != respuesta_6 && distractor_3 > 0 && !distractor_3 %in% distractores) {
  distractores <- c(distractores, distractor_3)
}

# Completar distractores con opciones plausibles si es necesario
opciones_adicionales <- c(90, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 
                          650, 700, 750, 800, 850, 900, 950)
opciones_adicionales <- opciones_adicionales[opciones_adicionales != respuesta_6]

for(opcion in sample(opciones_adicionales)) {
  if(length(distractores) >= 3) break
  if(!opcion %in% distractores) {
    distractores <- c(distractores, opcion)
  }
}

# Asegurar exactamente 3 distractores
distractores <- distractores[1:3]

# Crear opciones finales para schoice
opciones_schoice <- c(respuesta_6, distractores)
opciones_texto <- paste0("$", sapply(opciones_schoice, formato_estandar, decimales = 0))

# Mezclar opciones aleatoriamente
orden_aleatorio <- sample(1:4)
opciones_mezcladas <- opciones_texto[orden_aleatorio]

# Para schoice en cloze, necesitamos un vector lógico
# La respuesta correcta es siempre la primera en opciones_schoice (respuesta_6)
indice_respuesta_correcta <- which(orden_aleatorio == 1)
solucion_schoice <- rep(FALSE, 4)
solucion_schoice[indice_respuesta_correcta] <- TRUE

# También mantenemos el string para compatibilidad
solucion_schoice_string <- rep("0", 4)
solucion_schoice_string[indice_respuesta_correcta] <- "1"
solucion_schoice_string <- paste(solucion_schoice_string, collapse="")

# Vector de soluciones para formato cloze híbrido (6 numéricas + 1 schoice)
solucion_cloze <- list(
  respuesta_1,      # Paso 1: Cobro total
  respuesta_2,      # Paso 2: Valor fijo
  respuesta_3,      # Paso 3: Costo variable
  respuesta_4,      # Paso 4: Días de facturación
  respuesta_5,      # Paso 5: División decimal
  respuesta_6       # Paso 6: Resultado final
)

# Tipos de respuesta: 6 numéricas + 1 schoice
tipos_respuesta <- c("num", "num", "num", "num", "num", "num", "schoice")

# Tolerancias para respuestas numéricas (schoice no necesita tolerancia)
tolerancias <- c(0, 0, 0, 0, 0.0001, 0, 0)

# Detectar formato de salida para ajustes posteriores
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# PRUEBAS DE VALIDACIÓN MATEMÁTICA
test_that("Validación de datos generados híbridos", {
  expect_true(cobro_total >= 15000 && cobro_total <= 25000)
  expect_true(valor_fijo >= 3000 && valor_fijo <= 8000)
  expect_true(cobro_total > valor_fijo)  # El cobro total debe ser mayor al valor fijo
  expect_true(costo_promedio_diario > 0)
  expect_true(all(consumos > 0 & consumos <= 20))
  expect_equal(length(solucion_cloze), 6)  # 6 respuestas cloze numéricas
  expect_equal(length(tipos_respuesta), 7)  # 6 tipos numéricos + 1 schoice
  expect_equal(length(tolerancias), 7)  # 7 tolerancias
  expect_equal(length(opciones_schoice), 4)  # 4 opciones múltiples
  expect_equal(length(unique(opciones_schoice)), 4)  # Todas diferentes
  expect_true(nombre_masculino %in% nombres_masculinos)
  expect_true(nombre_femenino %in% nombres_femeninos)
  expect_true(nombre1 %in% c(nombre_masculino, nombre_femenino))
  expect_true(nombre2 %in% c(nombre_masculino, nombre_femenino))
  expect_true(nombre1 != nombre2)  # Los nombres deben ser diferentes
  expect_true(mes_pregunta %in% meses)
  expect_true(mes_factura %in% meses)
  expect_true(mes_pregunta != mes_factura)  # Los meses deben ser diferentes
})

test_that("Validación de coherencia matemática híbrida", {
  # Verificar que el cálculo del costo promedio diario es correcto
  costo_calculado <- redondear_matematico((cobro_total - valor_fijo) / 30, 0)
  expect_equal(costo_promedio_diario, costo_calculado)

  # Verificar coherencia entre respuestas cloze
  expect_equal(respuesta_1, cobro_total)  # Paso 1
  expect_equal(respuesta_2, valor_fijo)    # Paso 2
  expect_equal(respuesta_3, costo_consumo_variable) # Paso 3
  expect_equal(respuesta_4, 30)   # Paso 4
  expect_equal(respuesta_5, redondear_matematico(costo_consumo_variable / 30, 4))  # Paso 5
  expect_equal(respuesta_6, costo_promedio_diario)  # Paso 6

  # Verificar coherencia de la parte schoice
  expect_true(respuesta_6 %in% opciones_schoice)
  expect_true(all(distractores != respuesta_6))
  expect_equal(length(indice_respuesta_correcta), 1)  # Solo una respuesta correcta
  expect_true(indice_respuesta_correcta >= 1 && indice_respuesta_correcta <= 4)

  # Verificar que todas las respuestas cloze son numéricas
  expect_true(all(sapply(solucion_cloze, is.numeric)))
  # Verificar que el índice de respuesta correcta es válido
  expect_true(indice_respuesta_correcta >= 1 && indice_respuesta_correcta <= 4)
})

test_that("Validación de coherencia económica", {
  # Verificar que el costo consumo variable es correcto
  expect_equal(costo_consumo_variable, cobro_total - valor_fijo)

  # Verificar que el costo promedio diario es razonable
  expect_true(costo_promedio_diario >= 100 && costo_promedio_diario <= 1000)

  # Verificar que los consumos son razonables
  expect_true(all(consumos >= 10 & consumos <= 18))
})
```

```{r generar_grafico_barras_python, message=FALSE, warning=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Preparar datos para Python
meses_python <- paste0("['", paste(meses, collapse="', '"), "']")
consumos_python <- paste0("[", paste(consumos, collapse=", "), "]")

# Código Python para generar el gráfico de barras
codigo_python <- paste0("
import matplotlib
matplotlib.use('Agg')  # Usar backend no interactivo
import matplotlib.pyplot as plt
import numpy as np
import random

# Datos del gráfico
meses = ", meses_python, "
consumos = ", consumos_python, "

# Paletas de colores variadas
paletas_colores = [
    ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'],  # Colores suaves
    ['#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6', '#1ABC9C', '#E67E22'],  # Colores vibrantes
    ['#FF7675', '#74B9FF', '#00B894', '#FDCB6E', '#6C5CE7', '#A29BFE', '#FD79A8'],  # Colores modernos
    ['#D63031', '#0984E3', '#00B894', '#E17055', '#A29BFE', '#FD79A8', '#FDCB6E'],  # Colores intensos
    ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43', '#10AC84', '#EE5A24'],  # Colores dinámicos
    ['#FF6348', '#2ED573', '#1E90FF', '#FFA502', '#FF6B81', '#7BED9F', '#70A1FF']   # Colores equilibrados
]

# Seleccionar paleta aleatoria
paleta_seleccionada = random.choice(paletas_colores)

# Asignar colores aleatorios a cada barra
colores = []
for i, mes in enumerate(meses):
    if mes == '", mes_pregunta, "':
        # Color más oscuro para el mes de la pregunta
        colores.append('#2C3E50')  # Color oscuro universal para destacar
    else:
        colores.append(paleta_seleccionada[i % len(paleta_seleccionada)])

# Crear figura
plt.figure(figsize=(8, 5))

# Crear gráfico de barras
barras = plt.bar(meses, consumos, color=colores, edgecolor='black', linewidth=1, width=0.7)

# Configuración del gráfico
plt.xlabel('Mes', fontsize=11, fontweight='bold')
plt.ylabel('Consumo en metros cúbicos', fontsize=11, fontweight='bold')
plt.xticks(rotation=45, ha='right', fontsize=10)
plt.yticks(fontsize=10)

# Configurar límites del eje Y
plt.ylim(0, max(consumos) + 1)

# Añadir valores sobre las barras
for i, (mes, consumo) in enumerate(zip(meses, consumos)):
    plt.text(i, consumo + 0.1, str(consumo), ha='center', va='bottom', fontweight='bold', fontsize=9)

# Configurar grilla
plt.grid(True, axis='y', linestyle='--', alpha=0.7)

# Ajustar diseño
plt.tight_layout()

# Guardar en múltiples formatos para compatibilidad
plt.savefig('grafico_consumo_gas.png', dpi=150, bbox_inches='tight', transparent=False)
plt.savefig('grafico_consumo_gas.pdf', dpi=150, bbox_inches='tight', transparent=False)
plt.close()
")

# Ejecutar código Python para generar la figura
py_run_string(codigo_python)

# Validar que el gráfico se creó correctamente
test_that("Validación del gráfico Python", {
  expect_true(file.exists("grafico_consumo_gas.png"))
  expect_equal(length(meses), 7)  # 7 meses
  expect_equal(length(consumos), 7)  # 7 valores de consumo
})
```

Question
========

`r nombre1` y `r nombre2` viven en un `r tipo_vivienda` y comparten el pago de los gastos. Para saber cuál es el costo promedio del consumo diario de gas natural, se resta del cobro total de ese mes el valor por servicio fijo, y luego se divide entre 30, que es el número de días que se utiliza para facturar un mes. En `r tolower(mes_pregunta)` el cobro total fue de $`r formato_estandar(cobro_total, 0)` y el valor por servicio fijo fue de $`r formato_estandar(valor_fijo, 0)`. La gráfica muestra el consumo histórico en metros cúbicos de ese servicio.

```{r mostrar_grafico, echo=FALSE, results='asis', fig.align="center"}
# Mostrar la imagen del gráfico generado con Python
# Ajustar tamaño según el formato de salida
if (es_moodle) {
  cat("![](grafico_consumo_gas.png){width=70%}")
} else {
  cat("![](grafico_consumo_gas.png){width=80%}")
}
```

Para determinar el costo promedio diario del consumo de gas natural en `r tolower(mes_pregunta)`, resuelva paso a paso:

**IMPORTANTE - Formato de números:**

- **Valores monetarios**: Sin separador de miles, use punto para decimales
  - Ejemplo: $16850.32 (no $16.850,32 ni $16850,32)
  - Significado: dieciséis mil ochocientos cincuenta pesos con treinta y dos centavos
- **Respuestas numéricas con decimales**: Sin separador de miles, use punto para decimales
  - Ejemplo: 1234.5678 (no 1.234,5678 ni 1234,5678)
  - Significado: mil doscientos treinta y cuatro punto cinco seis siete ocho

### Paso 1: Identificación del cobro total
Según el enunciado del problema, ¿cuál fue el cobro total en `r tolower(mes_pregunta)`?

**Respuesta:** $##ANSWER1##

### Paso 2: Identificación del valor por servicio fijo
Según el enunciado del problema, ¿cuál fue el valor por servicio fijo en `r tolower(mes_pregunta)`?

**Respuesta:** $##ANSWER2##

### Paso 3: Cálculo del costo variable
Para obtener el costo variable, reste el valor por servicio fijo del cobro total:

Costo variable = $##ANSWER1## - $##ANSWER2## = $##ANSWER3##

### Paso 4: Identificación de días de facturación
Según el enunciado del problema, ¿cuántos días se utilizan para facturar un mes?

**Respuesta:** ##ANSWER4## días

### Paso 5: Cálculo de la división
Divida el costo variable entre el número de días de facturación (exprese su respuesta con 4 decimales usando punto como separador decimal):

$##ANSWER3## ÷ ##ANSWER4## = ##ANSWER5##

**Ejemplo de formato esperado:** 123.4567 (usando punto decimal)

### Paso 6: Resultado final redondeado
Redondee el resultado anterior a números (pesos) enteros:

##ANSWER5## $\approx$ $##ANSWER6##

### Paso 7: Confirmación mediante selección múltiple (CON PUNTUACIÓN)
Ahora que ha completado el análisis paso a paso, **confirme su respuesta seleccionando la opción correcta**. Esta selección también será evaluada y contribuirá a su puntuación final.

**Pregunta:** Basándose en su análisis anterior, ¿cuál es el costo promedio aproximado, en pesos, del consumo diario de `r tolower(mes_pregunta)`?

##ANSWER7##

**Conclusión:** El costo promedio diario del consumo de `r tolower(mes_pregunta)` es de $##ANSWER6##.

Answerlist
----------
* `r opciones_mezcladas[1]`
* `r opciones_mezcladas[2]`
* `r opciones_mezcladas[3]`
* `r opciones_mezcladas[4]`

Solution
========

### Análisis paso a paso del problema

Este problema de **cálculo de costo promedio diario** requiere un análisis secuencial que demuestre el proceso de razonamiento matemático:

**NOTA IMPORTANTE - Formato de números estandarizado:**

- **Valores monetarios**: Sin separador de miles, use punto para decimales
  - Ejemplo: $16850.32 (no $16.850,32 ni $16850,32)
- **Respuestas numéricas con decimales**: Sin separador de miles, punto como separador decimal
  - Ejemplo: 1234.5678 (no 1.234,5678 ni 1234,5678)
- **Consistencia**: Mismo formato en enunciado, opciones y respuestas

### Paso 1: Identificación correcta del cobro total ✓

**Respuesta correcta:** $`r formato_estandar(respuesta_1, 0)`

El enunciado establece claramente que "en `r tolower(mes_pregunta)` el cobro total fue de $`r formato_estandar(cobro_total, 0)`".

### Paso 2: Identificación correcta del valor por servicio fijo ✓

**Respuesta correcta:** $`r formato_estandar(respuesta_2, 0)`

El enunciado establece que "el valor por servicio fijo fue de $`r formato_estandar(valor_fijo, 0)`".

### Paso 3: Cálculo correcto del costo variable ✓

**Respuesta correcta:** $`r formato_estandar(respuesta_3, 0)`

Para obtener el costo variable, se debe restar el valor por servicio fijo del cobro total:

$$\text{Costo variable} = \$`r formato_estandar(cobro_total, 0)` - \$`r formato_estandar(valor_fijo, 0)` = \$`r formato_estandar(costo_consumo_variable, 0)`$$

### Paso 4: Identificación correcta de días de facturación ✓

**Respuesta correcta:** `r respuesta_4` días

El enunciado establece que "se divide entre 30, que es el número de días que se utiliza para facturar un mes".

### Paso 5: Cálculo preciso de la división ✓

**Respuesta correcta:** `r formato_estandar(respuesta_5, 4)`

$$\frac{\$`r formato_estandar(costo_consumo_variable, 0)`}{`r respuesta_4`} = `r formato_estandar(respuesta_5, 4)`$$

### Paso 6: Redondeo correcto del resultado final ✓

**Respuesta correcta:** $`r formato_estandar(respuesta_6, 0)`

El resultado de la división (`r formato_estandar(respuesta_5, 4)`) se redondea a pesos enteros: $`r formato_estandar(respuesta_6, 0)`.

### Paso 7: Confirmación mediante selección múltiple ✓ (CON PUNTUACIÓN)

**Opciones presentadas:**

```{r mostrar_opciones, echo=FALSE, results='asis'}
for(i in 1:4) {
  correcto <- if(i == indice_respuesta_correcta) " ← **RESPUESTA CORRECTA**" else ""
  cat(paste0("- **", LETTERS[i], "**: ", opciones_mezcladas[i], correcto, "\n"))
}
```

**Importancia de este paso:**

- **Contribuye a la puntuación final** (no es solo verificación)
- **Confirma** la comprensión del proceso analítico completo
- **Identifica** posibles errores en el razonamiento matemático
- **Refuerza** el aprendizaje mediante comparación con distractores educativos

### Verificación del proceso de razonamiento híbrido completo

**Resultado final:** $`r formato_estandar(respuesta_6, 0)`

**El formato híbrido con puntuación dual (cloze + schoice) garantiza que los estudiantes:**

**Parte Analítica (Pasos 1-6):**

- **Lean cuidadosamente** el enunciado para extraer datos económicos precisos
- **Identifiquen explícitamente** los valores del cobro total y servicio fijo
- **Apliquen correctamente** la fórmula de costo promedio diario paso a paso
- **Realicen cálculos** matemáticos sin saltar etapas del proceso
- **Redondeen apropiadamente** el resultado final

**Parte de Confirmación (Paso 7):**

- **Demuestren coherencia** entre su análisis y la respuesta final
- **Apliquen pensamiento crítico** al comparar con distractores
- **Consoliden su aprendizaje** mediante validación de resultados

```{r analisis_distractores, echo=FALSE, results='asis'}
cat("**Análisis de errores conceptuales comunes:**\n\n")

# Identificar qué tipo de distractores se generaron
for(i in seq_along(distractores)) {
  distractor <- distractores[i]

  if (distractor == redondear_matematico(cobro_total / 30, 0)) {
    cat(paste0("- **$", distractor, "**: Error conceptual - no restar el valor por servicio fijo\n"))
  } else if (distractor == redondear_matematico(costo_consumo_variable / 31, 0)) {
    cat(paste0("- **$", distractor, "**: Error de cálculo - dividir entre 31 días en lugar de 30\n"))
  } else if (distractor == redondear_matematico(valor_fijo / 30, 0)) {
    cat(paste0("- **$", distractor, "**: Error conceptual - usar solo el valor fijo dividido entre 30\n"))
  } else {
    cat(paste0("- **$", distractor, "**: Error de cálculo - aplicar incorrectamente la fórmula\n"))
  }
}
```

### Conclusión

El costo promedio diario del consumo de `r tolower(mes_pregunta)` es de **$`r formato_estandar(respuesta_6, 0)`**.

Esta respuesta es coherente porque:

- Se basa en una lectura correcta del enunciado
- Aplica correctamente la fórmula: (Cobro total - Valor fijo) ÷ 30
- El resultado es razonable para un costo diario de consumo de gas natural

**Verificación adicional**: La gráfica muestra el consumo histórico que contextualiza el problema, y los valores económicos son coherentes con el contexto familiar planteado.

Meta-information
================
exname: Consumo Gas Natural Costo Promedio Diario - Análisis Secuencial
extype: cloze
exsolution: `r paste(c(solucion_cloze[1:6], mchoice2string(solucion_schoice)), collapse="|")`
exclozetype: `r paste(tipos_respuesta, collapse="|")`
extol: `r paste(tolerancias, collapse="|")`
exsection: Aritmética|Cálculo de promedios|Análisis económico|Operaciones básicas
exextra[Type]: Cálculo
exextra[Program]: R
exextra[Language]: es
exextra[Level]: 2
exextra[Competencia]: Formulación y ejecución
exextra[Componente]: Numérico variacional
exextra[Contexto]: Familiar
exextra[Dificultad]: Media
