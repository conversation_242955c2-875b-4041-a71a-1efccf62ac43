# 🤖 COMANDOS GEMINI CLI - PROYECTO ICFES R-EXAMS

## 🚀 COMANDOS DE INICIO

### Cargar Contexto Completo
```
@Auxiliares/Instalaciones/Ais/Gemini_CLI/GEMINI.md
```

### Cargar Reglas Específicas
```
@Auxiliares/Instalaciones/Ais/Gemini_CLI/rules-gemini.md
```

### Cargar Plan de Tareas
```
@Auxiliares/Instalaciones/Ais/Gemini_CLI/task-list-gemini.md
```

## 🔍 COMANDOS DE ANÁLISIS

### Analizar <PERSON>cio Existente
```
Analiza este ejercicio: @path/to/ejercicio.Rmd
```

### Identificar Competencia ICFES
```
Identifica qué competencia ICFES evalúa mejor este ejercicio
```

### Revisar Calidad Pedagógica
```
Revisa la calidad pedagógica y técnica de este ejercicio
```

## 🎨 COMANDOS DE CREACIÓN

### Crear desde Imagen
```
Analiza esta imagen y aplica el sistema condicional automático
```

### Generar Ejercicio Completo
```
Crea un ejercicio ICFES completo siguiendo las mejores prácticas
```

### Replicar con TikZ
```
Replica esta imagen con TikZ manteniendo 98% fidelidad
```

## 🔧 COMANDOS DE OPTIMIZACIÓN

### Corregir Errores
```
Identifica y corrige errores siguiendo la metodología del proyecto
```

### Optimizar Código
```
Optimiza este código manteniendo la calidad pedagógica
```

### Validar Estándares
```
Valida que cumple estándares ICFES y genera 300+ versiones
```

## 🌐 COMANDOS DE INVESTIGACIÓN

### Buscar Información ICFES
```
Busca información actualizada sobre [competencia] ICFES 2025
```

### Validar Documentación Oficial
```
Valida que este ejercicio cumple estándares oficiales MEN
```

## 🧪 COMANDOS DE TESTING

### Probar Compilación
```
Compila este ejercicio en HTML, PDF y Word
```

### Validar Diversidad
```
Verifica que genera 300+ versiones únicas
```

### Testing Completo
```
Ejecuta testing completo de calidad y funcionamiento
```
