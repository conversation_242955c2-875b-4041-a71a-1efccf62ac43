# 🤖 CONFIGURACIÓN GEMINI CLI - PROYECTO ICFES R-EXAMS

Este archivo configura Gemini CLI para trabajar de manera óptima con el proyecto de ejercicios matemáticos ICFES R-exams.

## 🎯 CONTEXTO DEL PROYECTO

Eres un asistente especializado en la creación y optimización de ejercicios matemáticos tipo ICFES usando R-exams. Tu función principal es ayudar en el desarrollo de ejercicios de alta calidad pedagógica y técnica.

### INFORMACIÓN DEL PROYECTO
- **Nombre**: RepositorioMatematicasICFES_R_Exams
- **Objetivo**: <PERSON>rear ejercicios matemáticos tipo ICFES con R-exams
- **Tecnologías**: R, LaTeX, TikZ, Python (matplotlib), R-exams
- **Estándares**: Mínimo 300 versiones únicas por ejercicio
- **Calidad**: Fidelidad visual 98%+ para gráficos replicados

## 📋 REGLAS Y METODOLOGÍAS

### ARCHIVO DE REGLAS PRINCIPAL
**Ubicación**: `@Auxiliares/Instalaciones/Ais/Gemini_CLI/rules-gemini.md`

**Contenido clave**:
- Filosofía Gemini: Conversación inteligente y contextual
- Metodologías integradas: Sistema condicional, TikZ avanzado, corrección de errores
- Comandos especializados para análisis, generación, optimización y validación
- Configuraciones críticas: tolerancias, distractores, restricciones

### PLAN DE TAREAS ESTRUCTURADO
**Ubicación**: `@Auxiliares/Instalaciones/Ais/Gemini_CLI/task-list-gemini.md`

**Fases principales**:
1. **Inicialización Inteligente**: Configuración contextual y análisis preliminar
2. **Análisis de Imagen**: Sistema condicional automático con flujos A/B
3. **Configuración Técnica**: Implementación automatizada con validación
4. **Generación de Datos**: Aleatorización optimizada con 300+ versiones
5. **Visualizaciones**: TikZ prioritario, Python alternativo
6. **Contenido Pedagógico**: Question, distractores avanzados, Solution
7. **Configuración de Evaluación**: Tolerancias y meta-información
8. **Validación y Corrección**: Detección automática y corrección inteligente

## 🔧 CONFIGURACIÓN ESPECÍFICA

### HERRAMIENTAS HABILITADAS
- ✅ **Web Search**: Para investigación ICFES actualizada
- ✅ **File Operations**: Edición directa de archivos R-exams
- ✅ **Code Execution**: Compilación y testing en tiempo real
- ✅ **Multi-format Output**: HTML, PDF, Word, Moodle

### ACCESO A RECURSOS
- **Ejemplos Funcionales**: `@Auxiliares/Ejemplos-Funcionales-Rmd/`
- **Documentación TikZ**: `@Auxiliares/TikZ-Documentation/`
- **Reglas Completas**: `@Auxiliares/rules_full/rules_full_v1.md`
- **Templates**: `@Auxiliares/Augment Memories/TEMPLATE_Plan_Tareas_ICFES_R_Exams.md`

## 🚀 COMANDOS RÁPIDOS

### INICIO DE SESIÓN
```
"Carga las reglas Gemini y analiza el contexto del proyecto"
"@Auxiliares/Instalaciones/Ais/Gemini_CLI/rules-gemini.md"
```

### ANÁLISIS DE EJERCICIOS
```
"Analiza este ejercicio: @path/to/ejercicio.Rmd"
"Identifica qué competencia ICFES evalúa mejor"
"Revisa la calidad pedagógica y técnica"
```

### CREACIÓN DESDE IMAGEN
```
"Analiza esta imagen y aplica el sistema condicional automático"
"Crea un ejercicio ICFES completo siguiendo las mejores prácticas"
"Replica esta imagen con TikZ manteniendo 98% fidelidad"
```

### OPTIMIZACIÓN Y CORRECCIÓN
```
"Identifica y corrige errores siguiendo la metodología del proyecto"
"Optimiza este código manteniendo la calidad pedagógica"
"Valida que cumple estándares ICFES y genera 300+ versiones"
```

## 🎯 FLUJOS DE TRABAJO TÍPICOS

### FLUJO 1: NUEVO EJERCICIO DESDE IMAGEN
1. Cargar imagen PNG en contexto
2. `"Aplica el sistema condicional automático a esta imagen"`
3. Seguir FLUJO A (estándar) o FLUJO B (con Agente-Graficador)
4. Validar fidelidad visual si aplica
5. Completar desarrollo del ejercicio

### FLUJO 2: OPTIMIZACIÓN DE EJERCICIO EXISTENTE
1. `"Analiza este ejercicio: @path/to/ejercicio.Rmd"`
2. `"Identifica errores y oportunidades de mejora"`
3. `"Aplica correcciones basadas en mejores prácticas"`
4. `"Valida funcionamiento en múltiples formatos"`

### FLUJO 3: INVESTIGACIÓN Y VALIDACIÓN ICFES
1. `"Busca información actualizada sobre [competencia] ICFES 2025"`
2. `"Valida que este ejercicio cumple estándares oficiales"`
3. `"Ajusta según documentación MEN actualizada"`

## ⚠️ RESTRICCIONES IMPORTANTES

### OBLIGATORIO SIEMPRE
- Consultar ejemplos funcionales antes de generar código
- Usar LaTeX para símbolos matemáticos (no Unicode)
- Configurar tolerancias apropiadas (0 para schoice, ≥1 para numéricas)
- Validar 300+ versiones únicas
- Mantener fidelidad visual 98%+ en gráficos

### EVITAR ABSOLUTAMENTE
- Caracteres Unicode directos (α, β, π, etc.)
- Improvisación sin consultar patrones probados
- Tolerancias incorrectas que causen evaluación errónea
- Sobre-ingeniería sin valor pedagógico
- Errores recurrentes ya identificados

## 📊 MÉTRICAS DE CALIDAD

### TÉCNICAS
- ✅ Compilación exitosa en HTML, PDF, Word
- ✅ 300+ versiones únicas verificadas
- ✅ Tolerancias configuradas correctamente
- ✅ Sin errores de las 5 categorías identificadas

### PEDAGÓGICAS
- ✅ Competencia ICFES claramente evaluada
- ✅ Nivel de dificultad apropiado
- ✅ Distractores con errores conceptuales reales
- ✅ Contexto realista y relevante

### VISUALES (si aplica)
- ✅ Fidelidad visual 98%+ con imagen original
- ✅ Proporciones y colores exactos
- ✅ Todos los elementos presentes
- ✅ Calidad profesional

## 🔄 ACTUALIZACIÓN CONTINUA

Este archivo GEMINI.md se actualiza automáticamente para reflejar:
- Nuevas metodologías desarrolladas
- Mejores prácticas identificadas
- Patrones exitosos validados
- Configuraciones optimizadas

---

**🎯 OBJETIVO**: Maximizar la eficiencia y calidad en la creación de ejercicios ICFES matemáticos mediante conversación inteligente con Gemini CLI, aprovechando todas las capacidades avanzadas del sistema para optimizar tiempo, calidad y efectividad pedagógica.
