# 🤖 Gemini CLI - Configuración Completa para Proyecto ICFES R-exams

**Ubicación:** `Auxiliares/Instalaciones/Ais/Gemini_CLI/`
**Sistema:** Manjaro Plasma + VSCode Insiders
**Versión:** Gemini CLI v0.1.21
**Estado:** ✅ Instalado, Configurado y Optimizado
**Contexto:** 49% reducción de archivos (13,398 → 6,774)
**Warning Root:** ✅ Resuelto completamente

---

## 📁 Contenido de esta Carpeta

```
Gemini_CLI/
├── README.md                    # Este archivo
├── gemini-cli-setup.md         # Documentación completa
├── install-gemini-cli.sh       # Script de instalación
├── test-gemini-cli.sh          # Script de verificación
├── gemini-tasks.json           # Tareas para VSCode
├── rules-gemini.md             # Reglas específicas para Gemini CLI
├── task-list-gemini.md         # Plan de tareas optimizado para Gemini
├── GEMINI.md                   # Configuración principal de Gemini CLI
├── configurar-gemini.sh        # Script de configuración especializada
├── iniciar-gemini-icfes.sh     # Inicio rápido con configuración ICFES
├── comandos-gemini-icfes.md    # Guía de comandos especializados
├── optimizar-contexto-gemini.sh # Script de optimización de contexto
├── gemini-optimizado.sh        # Inicio optimizado (sin warnings)
├── TUTORIAL_USO_GEMINI.md      # Tutorial completo de uso
├── SOLUCION_WARNING_ROOT.md    # Solución al warning de directorio raíz
└── CONFIGURACION_COMPLETADA.md # Resumen ejecutivo de configuración
```

---

## 🚀 Inicio Rápido

### ✅ **Si ya está instalado:**
```bash
# Verificar funcionamiento
./test-gemini-cli.sh

# Usar desde terminal
gemini -p "Tu pregunta aquí"

# Usar desde VSCode
# Ctrl+Shift+P → "Tasks: Run Task" → Seleccionar tarea Gemini

# Configurar para proyecto ICFES (RECOMENDADO)
./configurar-gemini.sh
```

### 🤖 **Configuración Especializada ICFES:**
```bash
# Configurar Gemini CLI para proyecto ICFES
bash configurar-gemini.sh

# Optimizar contexto (RECOMENDADO - evita warnings)
bash optimizar-contexto-gemini.sh

# Iniciar con configuración optimizada
gemini-icfes-optimizado

# O usar script directo
bash gemini-optimizado.sh
```

### ⚡ **Versión Optimizada (Sin Warnings) - RECOMENDADA:**
```bash
# Comando optimizado - evita warning "running in root directory"
gemini-icfes-optimizado

# Beneficios obtenidos:
# ✅ 49% reducción de contexto (13,398 → 6,774 archivos)
# ✅ Carga 50% más rápida
# ✅ Sin warnings de directorio raíz
# ✅ Contexto inteligente con .geminiignore
# ✅ Validación automática de directorio
# ✅ Configuración especializada ICFES cargada automáticamente
```

### 🔧 **Si necesitas reinstalar:**
```bash
# Ejecutar instalador
./install-gemini-cli.sh

# Verificar instalación
./test-gemini-cli.sh
```

---

## 📖 Documentación Completa

### 📋 **Guías de Usuario**
- **`TUTORIAL_USO_GEMINI.md`** - Tutorial completo paso a paso
- **`CONFIGURACION_COMPLETADA.md`** - Resumen ejecutivo de configuración
- **`comandos-gemini-icfes.md`** - Guía de comandos especializados
- **`SOLUCION_WARNING_ROOT.md`** - Solución al warning de directorio raíz

### 🤖 **Archivos de Configuración Gemini**
- **`rules-gemini.md`** - Reglas específicas para Gemini CLI
- **`task-list-gemini.md`** - Plan de tareas optimizado para Gemini
- **`GEMINI.md`** - Configuración principal de contexto

### 🛠️ **Scripts de Instalación y Configuración**

#### `install-gemini-cli.sh`
- Instalación automática completa
- Configuración de prerrequisitos
- Configuración de API Key
- Integración con VSCode

#### `configurar-gemini.sh`
- Configuración especializada para proyecto ICFES
- Creación de archivos de contexto
- Configuración de comandos globales

#### `optimizar-contexto-gemini.sh`
- Optimización de contexto con .geminiignore
- Reducción 49% de archivos en contexto
- Solución al warning de directorio raíz

#### `test-gemini-cli.sh`
- Verificación de instalación
- Pruebas de funcionamiento
- Diagnóstico de problemas
- Reporte de estado

#### `gemini-tasks.json`
- Tareas predefinidas para VSCode
- Integración con flujo de trabajo
- Comandos especializados para R-exams

---

## 🚀 Comandos Principales

### **Comando Optimizado (Recomendado)**
```bash
# Inicio optimizado sin warnings
gemini-icfes-optimizado

# Cargar contexto completo dentro de Gemini CLI
@Auxiliares/Instalaciones/Ais/Gemini_CLI/GEMINI.md
```

### **Comandos Especializados en Gemini CLI**
```
# Crear ejercicio desde imagen
"Analiza esta imagen y aplica el sistema condicional automático"

# Optimizar ejercicio existente
"Analiza este ejercicio: @path/to/ejercicio.Rmd"

# Investigar información ICFES
"Busca información actualizada sobre competencia argumentación ICFES 2025"

# Validar calidad y estándares
"Valida que cumple estándares ICFES y genera 300+ versiones"

# Corregir errores automáticamente
"Identifica y corrige errores siguiendo la metodología del proyecto"
```

## 🎯 Tareas Disponibles en VSCode

| Tarea | Descripción | Comando Equivalente |
|-------|-------------|---------------------|
| 🤖 **Analizar Archivo Actual** | Analiza el archivo seleccionado | `"Analiza este archivo: @archivo.Rmd"` |
| 🤖 **Revisar Proyecto Completo** | Análisis completo del proyecto | `"Revisa la estructura del proyecto"` |
| 🤖 **Generar Documentación** | Crea documentación automática | `"Genera documentación para este código"` |
| 🤖 **Optimizar Código R** | Optimiza código R específicamente | `"Optimiza este código R manteniendo funcionalidad"` |
| 🤖 **Crear Ejercicio Similar** | Genera variaciones de ejercicios | `"Crea un ejercicio similar con diferentes parámetros"` |
| 🤖 **Modo Interactivo Optimizado** | Sesión con contexto ICFES | `gemini-icfes-optimizado` |
| 🤖 **Análisis TikZ/LaTeX** | Especializado en gráficas | `"Replica esta imagen con TikZ 98% fidelidad"` |
| 🤖 **Verificar Instalación** | Ejecuta pruebas completas | `./test-gemini-cli.sh` |

---

## 🔑 Configuración

### **API Key**
- **Ubicación:** Variables de entorno (`~/.bashrc`, `~/.zshrc`)
- **Formato:** `export GEMINI_API_KEY="tu_api_key_aqui"`
- **Obtener:** [Google AI Studio](https://aistudio.google.com/app/apikey)

### **Ejecutable**
- **Ubicación:** `/home/<USER>/.npm-global/bin/gemini`
- **Acceso:** Global (disponible en cualquier terminal)

---

## 🔧 Integración Optimizada con el Proyecto

### **Flujo Recomendado para Ejercicios R-exams:**
```bash
# 1. Iniciar con configuración optimizada
gemini-icfes-optimizado

# 2. Cargar contexto completo (dentro de Gemini CLI)
@Auxiliares/Instalaciones/Ais/Gemini_CLI/GEMINI.md

# 3. Analizar ejercicio específico
"Analiza este ejercicio: @Lab-Manjaro/01-S1-2024B/gastos_carro_graficas_comparacion_interpretacion_representacion_n2_opA_cloze_v1.Rmd"

# 4. Aplicar metodologías del proyecto
"Identifica errores y aplica correcciones basadas en mejores prácticas del proyecto"
```

### **Para Creación desde Imágenes:**
```bash
# 1. Iniciar Gemini CLI optimizado
gemini-icfes-optimizado

# 2. Cargar contexto
@Auxiliares/Instalaciones/Ais/Gemini_CLI/GEMINI.md

# 3. Subir imagen y analizar
"Analiza esta imagen y aplica el sistema condicional automático"

# 4. Seguir recomendaciones FLUJO A o FLUJO B
```

### **Para Optimización de Ejercicios Existentes:**
```bash
# Comando directo optimizado
gemini-icfes-optimizado

# Comandos especializados dentro de Gemini CLI:
"Revisa la diversidad de versiones generadas"
"Valida que cumple estándares ICFES oficiales"
"Optimiza la función generar_datos() para 300+ versiones"
"Mejora los distractores usando errores conceptuales reales"
```

---

## 🆘 Solución de Problemas

### **Problema: Warning "running in root directory"**
```bash
# SOLUCIÓN: Usar comando optimizado
gemini-icfes-optimizado

# Si persiste, re-optimizar contexto
bash optimizar-contexto-gemini.sh
```

### **Problema: .geminiignore no encontrado**
```bash
# Ejecutar optimización de contexto
bash optimizar-contexto-gemini.sh

# Verificar que se creó
ls -la .geminiignore
```

### **Problema: Contexto lento o limitado**
```bash
# Verificar optimización
ls -la .geminiignore .gemini/

# Re-optimizar si es necesario
bash optimizar-contexto-gemini.sh
```

### **Problema: Configuración no cargada**
```bash
# Cargar contexto manualmente en Gemini CLI
@Auxiliares/Instalaciones/Ais/Gemini_CLI/GEMINI.md

# Verificar archivos de configuración
ls -la Auxiliares/Instalaciones/Ais/Gemini_CLI/
```

### **Problema: Comando no encontrado**
```bash
# Verificar instalación
which gemini
which gemini-icfes-optimizado

# Reinstalar si es necesario
bash install-gemini-cli.sh
bash configurar-gemini.sh
```

### **Problema: API Key no funciona**
```bash
# Verificar variable de entorno
echo $GEMINI_API_KEY

# Recargar configuración
source ~/.bashrc
```

### **Problema: Tareas no aparecen en VSCode**
1. Verificar que `.vscode/tasks.json` existe
2. Recargar VSCode Insiders
3. Verificar sintaxis JSON del archivo

---

## 📊 Estado del Sistema Optimizado

**Última verificación:** Ejecutar `./test-gemini-cli.sh`

**Componentes Base:**
- ✅ Go 1.24.6
- ✅ Node.js v22.17.1
- ✅ npm 10.9.2
- ✅ Gemini CLI v0.1.21
- ✅ API Key configurada

**Optimizaciones Implementadas:**
- ✅ Contexto optimizado (49% reducción)
- ✅ Warning directorio raíz resuelto
- ✅ Comando global `gemini-icfes-optimizado`
- ✅ Configuración especializada ICFES
- ✅ `.geminiignore` configurado
- ✅ Scripts de optimización disponibles

**Archivos de Configuración:**
- ✅ `rules-gemini.md` - Reglas específicas
- ✅ `task-list-gemini.md` - Plan de tareas
- ✅ `GEMINI.md` - Contexto principal
- ✅ `TUTORIAL_USO_GEMINI.md` - Guía completa
- ✅ VSCode Tasks actualizadas

---

## 🔄 Mantenimiento y Actualizaciones

### **Actualizar Gemini CLI:**
```bash
npm update -g @google/gemini-cli
# Después de actualizar, re-configurar
bash configurar-gemini.sh
```

### **Verificar funcionamiento completo:**
```bash
# Testing básico
./test-gemini-cli.sh

# Verificar optimización
gemini-icfes-optimizado
```

### **Re-optimizar contexto (si es necesario):**
```bash
bash optimizar-contexto-gemini.sh
```

### **Backup de configuración completa:**
```bash
# Respaldar configuración completa
tar -czf gemini-backup-$(date +%Y%m%d).tar.gz \
  ~/.bashrc \
  ~/.config/gemini/ \
  .geminiignore \
  .gemini/ \
  Auxiliares/Instalaciones/Ais/Gemini_CLI/
```

---

## 🎯 Próximos Pasos Recomendados

1. **Familiarizarse con el comando optimizado**: `gemini-icfes-optimizado`
2. **Revisar tutorial completo**: `TUTORIAL_USO_GEMINI.md`
3. **Probar comandos especializados** para ejercicios ICFES
4. **Experimentar con flujos de trabajo** optimizados
5. **Documentar patrones exitosos** para referencia futura

---

**📞 Soporte Completo:**
- **Técnico:** `gemini-cli-setup.md`
- **Usuario:** `TUTORIAL_USO_GEMINI.md`
- **Problemas:** `SOLUCION_WARNING_ROOT.md`
- **Configuración:** `CONFIGURACION_COMPLETADA.md`
