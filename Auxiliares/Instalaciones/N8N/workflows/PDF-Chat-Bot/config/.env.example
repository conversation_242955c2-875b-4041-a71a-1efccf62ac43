# ========================================
# PDF-Chat-Bot - Variables de Entorno
# ========================================

# ==========================================
# CONFIGURACIÓN DEL PROYECTO
# ==========================================
PROJECT_NAME=PDF-Chat-Bot
PROJECT_VERSION=1.0.0
PROJECT_PATH="/home/<USER>/Insync/<EMAIL>/Google Drive/RepositorioMatematicasICFES_R_Exams/Auxiliares/Instalaciones/N8N/workflows/PDF-Chat-Bot"

# ==========================================
# RUTAS DE DIRECTORIOS
# ==========================================
# Carpeta principal de documentos
DOCUMENTS_PATH="${PROJECT_PATH}/data/documents"
PROCESSED_PATH="${PROJECT_PATH}/data/processed"
BACKUPS_PATH="${PROJECT_PATH}/data/backups"
LOGS_PATH="${PROJECT_PATH}/logs"

# Subcarpetas específicas
MATEMATICAS_PATH="${DOCUMENTS_PATH}/matematicas"
EXAMENES_PATH="${DOCUMENTS_PATH}/examenes"

# ==========================================
# CONFIGURACIÓN DE N8N
# ==========================================
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_WEBHOOK_URL="${N8N_PROTOCOL}://${N8N_HOST}:${N8N_PORT}/webhook"

# ==========================================
# API KEYS - COMPLETAR CON TUS CREDENCIALES
# ==========================================

# Anthropic API (Claude)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=2000

# OpenAI API (para embeddings)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1536

# OCR.space API (para procesamiento de imágenes)
OCR_SPACE_API_KEY=your_ocr_space_api_key_here
OCR_LANGUAGE=spa

# ==========================================
# CONFIGURACIÓN DE BASE DE DATOS
# ==========================================
# PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pdf_chatbot
DB_USER=pdf_chatbot_user
DB_PASSWORD=your_secure_password_here
DB_SSL=false

# Cadena de conexión completa
DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# ==========================================
# CONFIGURACIÓN DE QDRANT
# ==========================================
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION=documents
QDRANT_URL="http://${QDRANT_HOST}:${QDRANT_PORT}"

# Si usas Qdrant Cloud
# QDRANT_CLOUD_URL=your_qdrant_cloud_url
# QDRANT_API_KEY=your_qdrant_api_key

# ==========================================
# CONFIGURACIÓN DE PROCESAMIENTO
# ==========================================
# Tamaños de chunk para vectorización
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CHUNKS_PER_DOCUMENT=100

# Límites de archivos
MAX_FILE_SIZE_MB=50
SUPPORTED_EXTENSIONS=pdf,jpg,jpeg,png

# Configuración de búsqueda
SEARCH_LIMIT=5
SIMILARITY_THRESHOLD=0.7

# ==========================================
# CONFIGURACIÓN DEL CHATBOT
# ==========================================
# Configuración de respuestas
MAX_RESPONSE_LENGTH=2000
RESPONSE_LANGUAGE=es
INCLUDE_SOURCES=true

# Configuración de sesiones
SESSION_TIMEOUT_HOURS=24
MAX_CONVERSATIONS_PER_USER=100

# ==========================================
# CONFIGURACIÓN DE MONITOREO
# ==========================================
# Intervalo de monitoreo de archivos (minutos)
FILE_MONITOR_INTERVAL=5

# Configuración de logs
LOG_LEVEL=info
LOG_MAX_SIZE=10MB
LOG_MAX_FILES=5

# ==========================================
# CONFIGURACIÓN DE SEGURIDAD
# ==========================================
# Validación de archivos
ENABLE_FILE_VALIDATION=true
SCAN_FOR_MALWARE=false

# Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000

# ==========================================
# CONFIGURACIÓN DE DESARROLLO
# ==========================================
# Modo de desarrollo
DEBUG_MODE=false
VERBOSE_LOGGING=false

# URLs de desarrollo
DEV_WEBHOOK_URL=http://localhost:5678/webhook
DEV_WEB_URL=http://localhost:5678/webhook/web

# ==========================================
# CONFIGURACIÓN DE BACKUP
# ==========================================
# Configuración de respaldos automáticos
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# ==========================================
# CONFIGURACIÓN DE NOTIFICACIONES
# ==========================================
# Email para notificaciones (opcional)
NOTIFICATION_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# ==========================================
# CONFIGURACIÓN DE PERFORMANCE
# ==========================================
# Configuración de workers
MAX_CONCURRENT_PROCESSES=4
PROCESSING_TIMEOUT_SECONDS=300

# Cache
ENABLE_CACHE=true
CACHE_TTL_SECONDS=3600

# ==========================================
# URLS Y ENDPOINTS
# ==========================================
CHATBOT_ENDPOINT="${N8N_WEBHOOK_URL}/chatbot"
UPLOAD_ENDPOINT="${N8N_WEBHOOK_URL}/upload"
HISTORY_ENDPOINT="${N8N_WEBHOOK_URL}/history"
STATUS_ENDPOINT="${N8N_WEBHOOK_URL}/status"
WEB_INTERFACE_URL="${N8N_WEBHOOK_URL}/web/chatbot"
