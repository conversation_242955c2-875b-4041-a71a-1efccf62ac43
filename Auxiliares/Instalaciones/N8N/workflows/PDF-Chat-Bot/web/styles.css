/* ========================================
   PDF-Chat-Bot - Estilos CSS
   ======================================== */

/* Variables CSS */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease-in-out;
}

/* Reset y base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    height: 100vh;
    overflow: hidden;
}

/* Container principal */
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: var(--surface-color);
    box-shadow: var(--shadow-lg);
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.header-content h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.header-content p {
    font-size: 0.875rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Botones */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-send {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn-send:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.btn-send:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--secondary-color);
    padding: 0.5rem;
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-icon:hover {
    background: var(--background-color);
    color: var(--primary-color);
}

/* Chat container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--background-color);
}

/* Welcome message */
.welcome-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
}

.welcome-content {
    max-width: 600px;
    padding: 2rem;
}

.welcome-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.welcome-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.welcome-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.example-questions {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    text-align: left;
}

.example-questions h3 {
    font-size: 1.125rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.example-questions ul {
    list-style: none;
}

.example-questions li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.example-questions li:hover {
    background: var(--background-color);
    color: var(--primary-color);
}

/* Messages */
.messages-container {
    max-width: 800px;
    margin: 0 auto;
}

.message {
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary-color);
    color: white;
}

.message.assistant .message-avatar {
    background: var(--secondary-color);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.message-bubble {
    padding: 1rem 1.25rem;
    border-radius: 1.5rem;
    position: relative;
    word-wrap: break-word;
}

.message.user .message-bubble {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 0.5rem;
}

.message.assistant .message-bubble {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
}

.message-info {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-sources {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
}

.message-sources h4 {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.source-tag {
    display: inline-block;
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

.typing-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Input area */
.input-area {
    padding: 1rem 2rem;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
    background: var(--background-color);
    border: 2px solid var(--border-color);
    border-radius: 1.5rem;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    background: none;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    min-height: 24px;
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.char-count {
    font-weight: 500;
}

.shortcuts {
    opacity: 0.7;
}

/* Modales */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(80vh - 120px);
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
}

.toast {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    margin-bottom: 0.5rem;
    min-width: 300px;
    animation: toastSlideIn 0.3s ease-out;
}

.toast.success { border-left: 4px solid var(--success-color); }
.toast.warning { border-left: 4px solid var(--warning-color); }
.toast.error { border-left: 4px solid var(--error-color); }

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1200;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    text-align: center;
    color: var(--primary-color);
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Upload area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--background-color);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.upload-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.upload-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Progress bar */
.upload-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    display: block;
    text-align: center;
    margin-top: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

/* History search */
.history-search {
    position: relative;
    margin-bottom: 1rem;
}

.history-search input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.history-search i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

/* History list */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.history-item:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
}

.history-item-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.history-item-preview {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item-meta {
    font-size: 0.75rem;
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
}

/* Responsive */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .chat-container {
        padding: 0.5rem;
    }

    .input-area {
        padding: 1rem;
    }

    .message-content {
        max-width: 85%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}
